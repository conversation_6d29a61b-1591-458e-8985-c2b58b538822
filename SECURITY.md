# YTK Speed Tracker - Configuração de Segurança

## 🔒 Configuração Segura para Produção

### 1. Variáveis de Ambiente

Para produção, **NUNCA** deixe credenciais no código. Use variáveis de ambiente:

#### Criar ficheiro `.env` (não incluir no Git):
```bash
# MQTT Configuration
VITE_MQTT_BROKER_URL=wss://seu-broker.com:8884/mqtt
VITE_MQTT_USERNAME=seu_username
VITE_MQTT_PASSWORD=sua_password_segura
```

#### Adicionar ao `.gitignore`:
```
.env
.env.local
.env.production
```

### 2. Configuração do Servidor

#### Para desenvolvimento:
```bash
# Copiar exemplo
cp .env.example .env

# Editar com suas credenciais
nano .env

# Build e executar
npm run build
npm run preview
```

#### Para produção:
```bash
# Definir variáveis no servidor
export VITE_MQTT_BROKER_URL="wss://..."
export VITE_MQTT_USERNAME="..."
export VITE_MQTT_PASSWORD="..."

# Build
npm run build

# Servir ficheiros estáticos
```

### 3. Boas Práticas de Segurança

#### MQTT:
- ✅ Use sempre WSS (WebSocket Secure)
- ✅ Credenciais únicas por ambiente
- ✅ Rotação regular de passwords
- ✅ Restrições de IP quando possível
- ✅ Tópicos com permissões específicas

#### Aplicação:
- ✅ Validação de inputs implementada
- ✅ Sanitização de dados
- ✅ Tratamento seguro de erros
- ✅ Logs sem informações sensíveis

### 4. Configuração do HiveMQ Cloud

1. **Aceder ao HiveMQ Cloud Console**
2. **Criar novo cluster** (se necessário)
3. **Configurar Access Management:**
   - Username/Password únicos
   - Permissões específicas por tópico
   - Restrições de IP (opcional)

### 5. Deployment Seguro

#### Netlify/Vercel:
```bash
# Definir variáveis de ambiente no dashboard
VITE_MQTT_BROKER_URL=wss://...
VITE_MQTT_USERNAME=...
VITE_MQTT_PASSWORD=...
```

#### Docker:
```dockerfile
# Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
ARG VITE_MQTT_BROKER_URL
ARG VITE_MQTT_USERNAME  
ARG VITE_MQTT_PASSWORD
RUN npm run build
EXPOSE 4173
CMD ["npm", "run", "preview"]
```

### 6. Monitorização

- 📊 Logs de conexão MQTT
- 🔍 Alertas de falhas de autenticação
- 📈 Métricas de performance
- 🚨 Detecção de anomalias

### 7. Backup e Recuperação

- 💾 Backup regular das configurações
- 🔄 Procedimentos de rollback
- 📋 Documentação de recovery
- 🧪 Testes de disaster recovery

## ⚠️ IMPORTANTE

**NUNCA** faça commit de:
- Ficheiros `.env`
- Credenciais em código
- Chaves de API
- Passwords

**SEMPRE** use:
- Variáveis de ambiente
- Gestores de secrets
- Rotação de credenciais
- Princípio do menor privilégio
