import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { Wifi, WifiOff, Loader2, Bug } from 'lucide-react';
import { Toaster } from '@/components/ui/toaster';
import { useSpeedTracker } from '@/hooks/useSpeedTracker';
import ControlPanel from '@/components/ControlPanel';
import DevicePanel from '@/components/DevicePanel';
import Charts from '@/components/Charts';
import SpeedHistoryTable from '@/components/SpeedHistoryTable';
import ReactionHistoryTable from '@/components/ReactionHistoryTable';
import SpeedDisplay from '@/components/SpeedDisplay';
import { Button } from '@/components/ui/button';
import RunDetailModal from '@/components/RunDetailModal';
import DebugLog from '@/components/DebugLog';
import TimestampConverter from '@/components/TimestampConverter';
import ConfigureDevicesModal from '@/components/ConfigureDevicesModal';
import ModeSelector from '@/components/ModeSelector';
import ReactionGame from '@/components/ReactionGame';
import ReactionResults from '@/components/ReactionResults';
import ReactionCharts from '@/components/ReactionCharts';
import ReactionHistory from '@/components/ReactionHistory';
import ReactionHistoryTableSimple from '@/components/ReactionHistoryTableSimple';
import MqttCommandLog from '@/components/MqttCommandLog';
import SensorSimulator from '@/components/SensorSimulator';
import { useReactionGame } from '@/hooks/useReactionGame';
import { safeLocalStorageGet, safeLocalStorageSet } from '@/lib/storage';

function App() {
  const [distance, setDistance] = useState(() =>
    safeLocalStorageGet('app_distance', '20')
  );
  const [participantName, setParticipantName] = useState(() =>
    safeLocalStorageGet('app_participantName', '')
  );
  
  // Persistir configurações da aplicação quando mudarem
  useEffect(() => {
    safeLocalStorageSet('app_distance', distance);
    console.log('💾 Saved distance:', distance);
  }, [distance]);

  useEffect(() => {
    safeLocalStorageSet('app_participantName', participantName);
    console.log('💾 Saved participantName:', participantName);
  }, [participantName]);

  const onNewMeasurement = useCallback((newRun) => {
    setLatestRun(newRun);
  }, []);

  const {
    timestamps,
    participants,
    addParticipant,
    updateParticipant,
    addReactionGameToParticipant,
    isRunning,
    startMeasurement,
    resetMeasurement,
    mqttStatus,
    connectMqtt,
    disconnectMqtt,
    devices,
    addDevice,
    updateDevice,
    toggleDeviceActive,
    configureAllDevices,
    logMessages,
    clearLogs,
    publishMessage,
    addLog,
  } = useSpeedTracker({
    distance,
    participantName,
    onNewMeasurement,
    onReactionDetected: (mac, timestamp) => {
      const currentMode = appModeRef.current;
      console.log('🎯 Reaction detected in App.jsx:', { mac, timestamp, appMode: currentMode });
      if (currentMode === 'reaction') {
        console.log('📤 Calling reactionGame.processReaction');
        if (reactionGame && reactionGame.processReaction) {
          reactionGame.processReaction(mac, timestamp);
        } else {
          console.log('❌ reactionGame.processReaction not available');
        }
      } else {
        console.log('❌ Not in reaction mode, current mode:', currentMode);
      }
    }
  });

  // Hook do jogo de reação
  const reactionGame = useReactionGame({
    devices,
    toggleDeviceActive,
    sendCommandToDevice: (mac, commandData) => {
      // Se commandData é um objeto, converter para string JSON; senão, envolver em {command: ...}
      const payload = typeof commandData === 'object' ? JSON.stringify(commandData) : JSON.stringify({ command: commandData });
      const topic = `/Module/Command/${mac}`;

      // Log do comando para debug (mostrar como string)
      if (window.mqttCommandLogger) {
        window.mqttCommandLogger(topic, JSON.parse(payload), 'sent');
      }

      publishMessage(topic, payload);
    },
    mqttStatus,
    participantName,
    addReactionGameToParticipant
  });

  const [selectedRun, setSelectedRun] = useState(null);
  const [latestRun, setLatestRun] = useState(null);
  const [showDebug, setShowDebug] = useState(false);
  const [isConfigureModalOpen, setIsConfigureModalOpen] = useState(false);
  const [appMode, setAppMode] = useState(() =>
    safeLocalStorageGet('app_mode', 'speed')
  ); // 'speed' ou 'reaction'
  const appModeRef = useRef('speed');

  // Persistir appMode quando mudar
  useEffect(() => {
    safeLocalStorageSet('app_mode', appMode);
    console.log('💾 Saved appMode:', appMode);
  }, [appMode]);

  // Atualizar ref quando modo muda
  useEffect(() => {
    appModeRef.current = appMode;
    console.log('📝 AppMode ref updated to:', appMode);
  }, [appMode]);

  // Log quando modo muda
  const handleModeChange = (newMode) => {
    console.log('🔄 Mode changed from', appMode, 'to', newMode);
    setAppMode(newMode);
  };
  
  const bestRunForParticipant = useMemo(() => {
    const currentParticipant = participantName || (latestRun ? latestRun.participantName : null);
    if (!currentParticipant || !participants[currentParticipant]) {
      return null;
    }
    return participants[currentParticipant].runs.reduce((best, current) => 
      (current.speedKmh > (best.speedKmh || 0)) ? current : best, { speedKmh: 0 }
    );
  }, [participantName, latestRun, participants]);

  const handleReset = () => {
    resetMeasurement();
    setLatestRun(null);
  }

  const isConnected = mqttStatus === 'Connected';
  const isConnecting = mqttStatus === 'Connecting...';

  const handleConnectionToggle = () => {
    if (isConnected) {
      disconnectMqtt();
    } else {
      connectMqtt();
    }
  };

  const handleConfigureDevices = (params) => {
    configureAllDevices(params);
    setIsConfigureModalOpen(false);
  };

  const handleUpdateParticipant = (oldName, newName, newAge, newPhoto) => {
    updateParticipant(oldName, newName, newAge, newPhoto);
    if (participantName === oldName) {
      setParticipantName(newName);
    }
  };

  const handleUpdateDevice = (mac, newName) => {
    updateDevice(mac, newName);
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-4 font-sans">
      <Helmet>
        <title>YTK - Speed Tracker</title>
        <meta name="description" content="Sistema avançado para medir velocidade entre 3 dispositivos com precisão de timestamp via MQTT" />
      </Helmet>

      <div className="max-w-7xl mx-auto space-y-6">
        <header className="flex justify-between items-center py-4">
          <motion.div initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} className="flex items-center gap-3">
            <img alt="YTK Logo" className="h-16" src="https://storage.googleapis.com/hostinger-horizons-assets-prod/27f05ab1-a0c6-4b8a-a93b-8c5eb7cd37dc/131f8e806cc7def5baa0c6d0c8f32d4b.png" />
          </motion.div>
          <motion.div initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} className="flex items-center gap-2">
            <Button onClick={() => setShowDebug(prev => !prev)} size="sm" variant={showDebug ? "secondary" : "outline"}>
              <Bug className="w-4 h-4 mr-2"/> Debug
            </Button>
            <Button onClick={handleConnectionToggle} size="sm" variant={isConnected ? "destructive" : "default"} className="rounded-full w-32" disabled={isConnecting}>
              {isConnecting ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : (isConnected ? <WifiOff className="w-4 h-4 mr-2" /> : <Wifi className="w-4 h-4 mr-2" />)}
              {isConnecting ? 'Conectando...' : (isConnected ? 'Desconectar' : 'Conectar')}
            </Button>
          </motion.div>
        </header>

        <main className="space-y-8">
          {appMode === 'speed' ? (
            // Modo Velocidade (original)
            <>
              <SpeedDisplay
                measurement={latestRun}
                bestRun={bestRunForParticipant}
                participantName={participantName}
                key={latestRun ? latestRun.id : 'no-run'}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ControlPanel
              distance={distance}
              setDistance={setDistance}
              participantName={participantName}
              setParticipantName={setParticipantName}
              participants={participants}
              addParticipant={addParticipant}
              isRunning={isRunning}
              startMeasurement={startMeasurement}
              resetMeasurement={handleReset}
              mqttStatus={mqttStatus}
            />
            <DevicePanel
              devices={devices}
              toggleDeviceActive={toggleDeviceActive}
              isConnected={isConnected}
              addDevice={addDevice}
              updateDevice={handleUpdateDevice}
              onConfigure={() => setIsConfigureModalOpen(true)}
              // Configurações de Dispositivos
              distanceThreshold={reactionGame.distanceThreshold}
              setDistanceThreshold={reactionGame.setDistanceThreshold}
              globalTimeout={reactionGame.globalTimeout}
              setGlobalTimeout={reactionGame.setGlobalTimeout}
              lastResortTimeout={reactionGame.lastResortTimeout}
              setLastResortTimeout={reactionGame.setLastResortTimeout}
              publishMessage={publishMessage}
            />
              </div>

              <Charts
                participants={participants}
              />
              <SpeedHistoryTable
                participants={participants}
                setParticipantName={setParticipantName}
                onSelectRun={setSelectedRun}
                selectedRun={selectedRun}
                updateParticipant={updateParticipant}
              />
            </>
          ) : (
            // Modo Reação
            <>
              <ReactionGame
                {...reactionGame}
                mqttStatus={mqttStatus}
                participantName={participantName}
                setParticipantName={setParticipantName}
                participants={participants}
                addParticipant={addParticipant}
                setAppMode={setAppMode}
                devices={devices}
                toggleDeviceActive={toggleDeviceActive}
                isConnectedProp={isConnected}
                addDevice={addDevice}
                updateDevice={handleUpdateDevice}
                publishMessage={publishMessage}
              />

              {/* DevicePanel agora está integrado no ReactionGame */}

              <ReactionResults
                reactions={reactionGame.reactions}
                gameStats={{
                  totalRounds: reactionGame.totalRounds,
                  successes: reactionGame.successes,
                  misses: reactionGame.misses,
                  accuracy: reactionGame.accuracy,
                  averageReactionTime: reactionGame.averageReactionTime,
                  bestReactionTime: reactionGame.reactions.filter(r => r.success).length > 0
                    ? Math.min(...reactionGame.reactions.filter(r => r.success).map(r => r.reactionTime))
                    : 0,
                  worstReactionTime: reactionGame.reactions.filter(r => r.success).length > 0
                    ? Math.max(...reactionGame.reactions.filter(r => r.success).map(r => r.reactionTime))
                    : 0,
                  maxSpeed: reactionGame.maxSpeed,
                  gameTime: reactionGame.gameTime
                }}
                participantName={participantName}
              />
              <ReactionCharts
                participants={participants}
              />
              <ReactionHistoryTable
                participants={participants}
                setParticipantName={setParticipantName}
                updateParticipant={updateParticipant}
              />
            </>
          )}



          {showDebug && (
            <motion.div initial={{ opacity: 0, height: 0 }} animate={{ opacity: 1, height: 'auto' }} exit={{ opacity: 0, height: 0 }}>
              <div className="space-y-4">
                <DebugLog logs={logMessages} onClear={clearLogs} />
                <TimestampConverter distance={distance} />
                {appMode === 'reaction' && (
                  <>
                    <MqttCommandLog isVisible={true} />
                    <SensorSimulator
                      devices={devices}
                      publishMessage={publishMessage}
                      isConnected={mqttStatus === 'Connected'}
                    />
                  </>
                )}
              </div>
            </motion.div>
          )}
        </main>
      </div>

      <RunDetailModal 
        run={selectedRun}
        isOpen={!!selectedRun}
        onOpenChange={() => setSelectedRun(null)}
      />

      <ConfigureDevicesModal
        isOpen={isConfigureModalOpen}
        onOpenChange={setIsConfigureModalOpen}
        onConfigure={handleConfigureDevices}
      />

      <Toaster />

      {/* Rodapé com Seletor de Modo */}
      <footer className="mt-12 pt-8 border-t border-border/40">
        <div className="flex flex-col items-center space-y-4">
          <div className="text-sm text-muted-foreground">
            Selecione o modo de operação
          </div>
          <ModeSelector currentMode={appMode} onModeChange={handleModeChange} />
          <div className="text-xs text-muted-foreground/60 text-center">
            YTK Speed Tracker - Sistema de Medição de Velocidade e Reação
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;