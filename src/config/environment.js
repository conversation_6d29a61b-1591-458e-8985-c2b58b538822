/**
 * Environment Configuration
 * Centraliza todas as configurações de ambiente
 */

// Configuração MQTT
export const MQTT_CONFIG = {
  brokerUrl: import.meta.env.VITE_MQTT_BROKER_URL || 'wss://9eb47e4fb03b46faa64d85e7e6498735.s2.eu.hivemq.cloud:8884/mqtt',
  username: import.meta.env.VITE_MQTT_USERNAME || 'circuit',
  password: import.meta.env.VITE_MQTT_PASSWORD || 'Genie#12',
  topics: {
    telemetry: '/Module/Telemetry/',
    command: '/Module/Command/'
  },
  options: {
    clean: true,
    reconnectPeriod: 5000,
    connectTimeout: 10000,
    keepalive: 60
  }
};

// Configuração da Aplicação
export const APP_CONFIG = {
  name: import.meta.env.VITE_APP_NAME || 'YTK Speed Tracker',
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',
  debugMode: import.meta.env.VITE_DEBUG_MODE === 'true',
  maxDevices: parseInt(import.meta.env.VITE_MAX_DEVICES) || 10,
  enableSimulation: import.meta.env.VITE_ENABLE_SIMULATION !== 'false'
};

// Configuração de Validação
export const VALIDATION_CONFIG = {
  distance: {
    min: 1,
    max: 1000,
    unit: 'metros'
  },
  participantName: {
    minLength: 2,
    maxLength: 50
  },
  speed: {
    maxKmh: 200,
    maxTimeSeconds: 300
  }
};

// Configuração de Performance
export const PERFORMANCE_CONFIG = {
  debounceDelay: 500,
  maxLogEntries: 100,
  cacheTimeout: 300000 // 5 minutos
};

// Utilitários
export const isDevelopment = () => import.meta.env.DEV;
export const isProduction = () => import.meta.env.PROD;
export const getEnvironment = () => import.meta.env.MODE;

// Validação de configuração
export const validateConfig = () => {
  const errors = [];
  
  if (!MQTT_CONFIG.brokerUrl.startsWith('wss://')) {
    errors.push('MQTT Broker URL deve usar WSS (seguro)');
  }
  
  if (!MQTT_CONFIG.username || !MQTT_CONFIG.password) {
    errors.push('Credenciais MQTT são obrigatórias');
  }
  
  if (VALIDATION_CONFIG.distance.max < VALIDATION_CONFIG.distance.min) {
    errors.push('Configuração de distância inválida');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Log de configuração (apenas em desenvolvimento)
if (isDevelopment()) {
  console.log('🔧 Configuração da Aplicação:', {
    environment: getEnvironment(),
    mqtt: {
      broker: MQTT_CONFIG.brokerUrl,
      username: MQTT_CONFIG.username,
      password: '***'
    },
    app: APP_CONFIG,
    validation: validateConfig()
  });
}
