import React, { useState, useMemo, useCallback } from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { Wifi, WifiOff, Loader2, Bug } from 'lucide-react';
import { Toaster } from '@/components/ui/toaster';
import { useSpeedTracker } from '@/hooks/useSpeedTracker';
import ControlPanel from '@/components/ControlPanel';
import DevicePanel from '@/components/DevicePanel';
import Charts from '@/components/Charts';
import HistoryTable from '@/components/HistoryTable';
import SpeedDisplay from '@/components/SpeedDisplay';
import { Button } from '@/components/ui/button';
import RunDetailModal from '@/components/RunDetailModal';
import DebugLog from '@/components/DebugLog';
import TimestampConverter from '@/components/TimestampConverter';
import ConfigureDevicesModal from '@/components/ConfigureDevicesModal';
import ModeSelector from '@/components/ModeSelector';
import ReactionGame from '@/components/ReactionGame';
import ReactionResults from '@/components/ReactionResults';
import ReactionHistory from '@/components/ReactionHistory';
import MqttCommandLog from '@/components/MqttCommandLog';
import SensorSimulator from '@/components/SensorSimulator';
import { useReactionGame } from '@/hooks/useReactionGame';

function App() {
  const [distance, setDistance] = useState('20');
  const [participantName, setParticipantName] = useState('');
  
  const onNewMeasurement = useCallback((newRun) => {
    setLatestRun(newRun);
  }, []);

  const {
    timestamps,
    participants,
    addParticipant,
    updateParticipant,
    addReactionGameToParticipant,
    isRunning,
    startMeasurement,
    resetMeasurement,
    mqttStatus,
    connectMqtt,
    disconnectMqtt,
    devices,
    addDevice,
    updateDevice,
    toggleDeviceActive,
    configureAllDevices,
    logMessages,
    clearLogs,
    publishMessage,
    addLog,
  } = useSpeedTracker({
    distance,
    participantName,
    onNewMeasurement,
    onReactionDetected: (mac, timestamp) => {
      console.log('🎯 Reaction detected in App.jsx:', { mac, timestamp, appMode });
      if (appMode === 'reaction') {
        console.log('📤 Calling reactionGame.processReaction');
        reactionGame.processReaction(mac, timestamp);
      }
    }
  });

  // Hook do jogo de reação
  const reactionGame = useReactionGame({
    devices,
    sendCommandToDevice: (mac, commandData) => {
      // Se commandData é um objeto, converter para string JSON; senão, envolver em {command: ...}
      const payload = typeof commandData === 'object' ? JSON.stringify(commandData) : JSON.stringify({ command: commandData });
      const topic = `/Module/Command/${mac}`;

      // Log do comando para debug (mostrar como string)
      if (window.mqttCommandLogger) {
        window.mqttCommandLogger(topic, JSON.parse(payload), 'sent');
      }

      publishMessage(topic, payload);
    },
    mqttStatus,
    participantName,
    addReactionGameToParticipant
  });

  const [selectedRun, setSelectedRun] = useState(null);
  const [latestRun, setLatestRun] = useState(null);
  const [showDebug, setShowDebug] = useState(false);
  const [isConfigureModalOpen, setIsConfigureModalOpen] = useState(false);
  const [appMode, setAppMode] = useState('speed'); // 'speed' ou 'reaction'
  
  const bestRunForParticipant = useMemo(() => {
    const currentParticipant = participantName || (latestRun ? latestRun.participantName : null);
    if (!currentParticipant || !participants[currentParticipant]) {
      return null;
    }
    return participants[currentParticipant].runs.reduce((best, current) => 
      (current.speedKmh > (best.speedKmh || 0)) ? current : best, { speedKmh: 0 }
    );
  }, [participantName, latestRun, participants]);

  const handleReset = () => {
    resetMeasurement();
    setLatestRun(null);
  }

  const isConnected = mqttStatus === 'Connected';
  const isConnecting = mqttStatus === 'Connecting...';

  const handleConnectionToggle = () => {
    if (isConnected) {
      disconnectMqtt();
    } else {
      connectMqtt();
    }
  };

  const handleConfigureDevices = (params) => {
    configureAllDevices(params);
    setIsConfigureModalOpen(false);
  };

  const handleUpdateParticipant = (oldName, newName, newAge, newPhoto) => {
    updateParticipant(oldName, newName, newAge, newPhoto);
    if (participantName === oldName) {
      setParticipantName(newName);
    }
  };

  const handleUpdateDevice = (mac, newName) => {
    updateDevice(mac, newName);
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-4 font-sans">
      <Helmet>
        <title>YTK - Speed Tracker</title>
        <meta name="description" content="Sistema avançado para medir velocidade entre 3 dispositivos com precisão de timestamp via MQTT" />
      </Helmet>

      <div className="max-w-7xl mx-auto space-y-6">
        <header className="flex justify-between items-center py-4">
          <motion.div initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} className="flex items-center gap-3">
            <img alt="YTK Logo" className="h-16" src="https://storage.googleapis.com/hostinger-horizons-assets-prod/27f05ab1-a0c6-4b8a-a93b-8c5eb7cd37dc/131f8e806cc7def5baa0c6d0c8f32d4b.png" />
          </motion.div>
          <motion.div initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} className="flex items-center gap-2">
            <Button onClick={() => setShowDebug(prev => !prev)} size="sm" variant={showDebug ? "secondary" : "outline"}>
              <Bug className="w-4 h-4 mr-2"/> Debug
            </Button>
            <Button onClick={handleConnectionToggle} size="sm" variant={isConnected ? "destructive" : "default"} className="rounded-full w-32" disabled={isConnecting}>
              {isConnecting ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : (isConnected ? <WifiOff className="w-4 h-4 mr-2" /> : <Wifi className="w-4 h-4 mr-2" />)}
              {isConnecting ? 'Conectando...' : (isConnected ? 'Desconectar' : 'Conectar')}
            </Button>
          </motion.div>
        </header>

        <main className="space-y-8">
          {/* Seletor de Modo */}
          <ModeSelector currentMode={appMode} onModeChange={setAppMode} />

          {appMode === 'speed' ? (
            // Modo Velocidade (original)
            <>
              <SpeedDisplay
                measurement={latestRun}
                bestRun={bestRunForParticipant}
                participantName={participantName}
                key={latestRun ? latestRun.id : 'no-run'}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ControlPanel
              distance={distance}
              setDistance={setDistance}
              participantName={participantName}
              setParticipantName={setParticipantName}
              participants={participants}
              addParticipant={addParticipant}
              isRunning={isRunning}
              startMeasurement={startMeasurement}
              resetMeasurement={handleReset}
              mqttStatus={mqttStatus}
            />
            <DevicePanel
              devices={devices}
              toggleDeviceActive={toggleDeviceActive}
              isConnected={isConnected}
              addDevice={addDevice}
              updateDevice={handleUpdateDevice}
              onConfigure={() => setIsConfigureModalOpen(true)}
            />
              </div>

              <Charts
                participants={participants}
              />
              <HistoryTable
                participants={participants}
                onSelectRun={setSelectedRun}
                selectedRun={selectedRun}
              />
            </>
          ) : (
            // Modo Reação
            <>
              <ReactionGame
                {...reactionGame}
                mqttStatus={mqttStatus}
                participantName={participantName}
                setParticipantName={setParticipantName}
                participants={participants}
                addParticipant={addParticipant}
              />
              <ReactionResults
                reactions={reactionGame.reactions}
                gameStats={{
                  successes: reactionGame.successes,
                  misses: reactionGame.misses,
                  accuracy: reactionGame.accuracy,
                  averageReactionTime: reactionGame.averageReactionTime,
                  maxSpeed: reactionGame.maxSpeed,
                  gameTime: reactionGame.gameTime
                }}
                participantName={participantName}
              />
              <ReactionHistory
                participants={participants}
                participantName={participantName}
              />
            </>
          )}

          {/* DevicePanel sempre visível */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div></div> {/* Espaço vazio */}
            <DevicePanel
              devices={devices}
              toggleDeviceActive={toggleDeviceActive}
              isConnected={isConnected}
              addDevice={addDevice}
              updateDevice={handleUpdateDevice}
              onConfigure={() => setIsConfigureModalOpen(true)}
            />
          </div>

          {showDebug && (
            <motion.div initial={{ opacity: 0, height: 0 }} animate={{ opacity: 1, height: 'auto' }} exit={{ opacity: 0, height: 0 }}>
              <div className="space-y-4">
                <DebugLog logs={logMessages} onClear={clearLogs} />
                <TimestampConverter distance={distance} />
                {appMode === 'reaction' && (
                  <>
                    <MqttCommandLog isVisible={true} />
                    <SensorSimulator
                      devices={devices}
                      publishMessage={publishMessage}
                      isConnected={mqttStatus === 'Connected'}
                    />
                  </>
                )}
              </div>
            </motion.div>
          )}
        </main>
      </div>

      <RunDetailModal 
        run={selectedRun}
        isOpen={!!selectedRun}
        onOpenChange={() => setSelectedRun(null)}
      />

      <ConfigureDevicesModal
        isOpen={isConfigureModalOpen}
        onOpenChange={setIsConfigureModalOpen}
        onConfigure={handleConfigureDevices}
      />

      <Toaster />
    </div>
  );
}

export default App;