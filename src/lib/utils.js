import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import <PERSON> from 'papa<PERSON><PERSON>';

export function cn(...inputs) {
	return twMerge(clsx(inputs));
}

export function exportToCSV(data, filename) {
  const csv = Papa.unparse(data);
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}