/**
 * Input validation utilities for the YTK Speed Tracker
 */

// Distance validation
export const validateDistance = (distance) => {
  const num = parseFloat(distance);
  if (isNaN(num)) {
    return { isValid: false, error: 'A distância deve ser um número válido' };
  }
  if (num <= 0) {
    return { isValid: false, error: 'A distância deve ser maior que zero' };
  }
  if (num > 1000) {
    return { isValid: false, error: 'A distância não pode exceder 1000 metros' };
  }
  return { isValid: true, value: num };
};

// Name sanitization and validation
export const sanitizeName = (name) => {
  if (!name || typeof name !== 'string') {
    return '';
  }
  // Remove potentially dangerous characters and trim
  return name.trim().replace(/[<>\"'&]/g, '').substring(0, 50);
};

export const validateParticipantName = (name) => {
  const sanitized = sanitizeName(name);
  if (!sanitized) {
    return { isValid: false, error: 'O nome do participante é obrigatório' };
  }
  if (sanitized.length < 2) {
    return { isValid: false, error: 'O nome deve ter pelo menos 2 caracteres' };
  }
  if (sanitized.length > 50) {
    return { isValid: false, error: 'O nome não pode exceder 50 caracteres' };
  }
  return { isValid: true, value: sanitized };
};

// MAC address validation
export const validateMacAddress = (mac) => {
  if (!mac || typeof mac !== 'string') {
    return { isValid: false, error: 'Endereço MAC é obrigatório' };
  }
  
  const cleanMac = mac.trim().toUpperCase();
  const macRegex = /^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/;
  
  if (!macRegex.test(cleanMac)) {
    return { isValid: false, error: 'Formato de MAC inválido. Use XX:XX:XX:XX:XX:XX' };
  }
  
  if (cleanMac.length !== 17) {
    return { isValid: false, error: 'MAC deve ter exatamente 17 caracteres' };
  }
  
  return { isValid: true, value: cleanMac.replace(/-/g, ':') };
};

// Numeric parameter validation for device configuration
export const validateNumericParameter = (value, min = 0, max = 10000, name = 'Parâmetro') => {
  const num = parseInt(value, 10);
  if (isNaN(num)) {
    return { isValid: false, error: `${name} deve ser um número válido` };
  }
  if (num < min) {
    return { isValid: false, error: `${name} deve ser pelo menos ${min}` };
  }
  if (num > max) {
    return { isValid: false, error: `${name} não pode exceder ${max}` };
  }
  return { isValid: true, value: num };
};

// Timestamp validation
export const validateTimestamp = (timestamp) => {
  const num = Number(timestamp);
  if (isNaN(num)) {
    return { isValid: false, error: 'Timestamp inválido' };
  }
  
  // Check if timestamp is reasonable (not too old, not in future)
  const now = Date.now();
  const oneHourAgo = now - (60 * 60 * 1000);
  const oneMinuteFromNow = now + (60 * 1000);
  
  if (num < oneHourAgo) {
    return { isValid: false, error: 'Timestamp muito antigo' };
  }
  if (num > oneMinuteFromNow) {
    return { isValid: false, error: 'Timestamp no futuro' };
  }
  
  return { isValid: true, value: num };
};

// Speed calculation validation
export const validateSpeedCalculation = (timestamps, distance) => {
  if (!Array.isArray(timestamps) || timestamps.length < 2) {
    return { isValid: false, error: 'Pelo menos 2 timestamps são necessários' };
  }
  
  const distanceValidation = validateDistance(distance);
  if (!distanceValidation.isValid) {
    return distanceValidation;
  }
  
  // Validate all timestamps
  for (const ts of timestamps) {
    const tsValidation = validateTimestamp(ts.timestamp);
    if (!tsValidation.isValid) {
      return { isValid: false, error: `Timestamp inválido: ${tsValidation.error}` };
    }
  }
  
  // Check for reasonable time differences
  const sortedTimestamps = [...timestamps].sort((a, b) => a.timestamp - b.timestamp);
  const totalTime = (sortedTimestamps[sortedTimestamps.length - 1].timestamp - sortedTimestamps[0].timestamp) / 1000;
  
  if (totalTime <= 0) {
    return { isValid: false, error: 'Tempo total deve ser positivo' };
  }
  
  if (totalTime > 300) { // 5 minutes max
    return { isValid: false, error: 'Tempo total muito longo (máximo 5 minutos)' };
  }
  
  // Calculate speed and check if reasonable
  const speed = distanceValidation.value / totalTime;
  const speedKmh = speed * 3.6;
  
  if (speedKmh > 200) { // 200 km/h max reasonable speed
    return { isValid: false, error: 'Velocidade calculada muito alta (máximo 200 km/h)' };
  }
  
  return { isValid: true, speed, speedKmh, totalTime };
};
