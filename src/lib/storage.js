/**
 * Safe localStorage utilities with error handling
 */

// Safe localStorage getter
export const safeLocalStorageGet = (key, defaultValue = null) => {
  try {
    if (typeof window === 'undefined' || !window.localStorage) {
      return defaultValue;
    }
    
    const item = localStorage.getItem(key);
    if (item === null) {
      return defaultValue;
    }
    
    return JSON.parse(item);
  } catch (error) {
    console.error(`Failed to read from localStorage (key: ${key}):`, error);
    return defaultValue;
  }
};

// Safe localStorage setter
export const safeLocalStorageSet = (key, value) => {
  try {
    if (typeof window === 'undefined' || !window.localStorage) {
      return false;
    }
    
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`Failed to write to localStorage (key: ${key}):`, error);
    return false;
  }
};

// Safe localStorage remover
export const safeLocalStorageRemove = (key) => {
  try {
    if (typeof window === 'undefined' || !window.localStorage) {
      return false;
    }
    
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error(`Failed to remove from localStorage (key: ${key}):`, error);
    return false;
  }
};

// Check if localStorage is available
export const isLocalStorageAvailable = () => {
  try {
    if (typeof window === 'undefined' || !window.localStorage) {
      return false;
    }
    
    const testKey = '__localStorage_test__';
    localStorage.setItem(testKey, 'test');
    localStorage.removeItem(testKey);
    return true;
  } catch (error) {
    return false;
  }
};
