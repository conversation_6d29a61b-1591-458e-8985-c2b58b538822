@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 210 40% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 210 40% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 38 92% 50%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 210 40% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 38 92% 50%;
    --radius: 0.5rem;
  }
}

body {
  @apply bg-background text-foreground;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  min-height: 100vh;
}

* {
  @apply border-border;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--secondary));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.5);
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.7);
}

/* Scroll personalizado laranja para seletor de participantes */
.participant-list-custom::-webkit-scrollbar {
  width: 8px;
}

.participant-list-custom::-webkit-scrollbar-track {
  background: transparent;
}

.participant-list-custom::-webkit-scrollbar-thumb {
  background-color: hsl(var(--primary));
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.participant-list-custom::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--primary) / 0.8);
}

/* Firefox */
.participant-list-custom {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--primary)) transparent;
}