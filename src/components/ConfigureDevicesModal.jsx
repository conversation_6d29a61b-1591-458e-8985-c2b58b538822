import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { validateNumericParameter } from '@/lib/validation';

const ConfigureDevicesModal = ({ isOpen, onOpenChange, onConfigure }) => {
  const [distancethreshold, setDistancethreshold] = useState('150');
  const [globaltimeout, setGlobaltimeout] = useState('5000'); // Corrigido para 5000 (mesmo que useReactionGame.js)
  const [lastresorttimeout, setLastresorttimeout] = useState('3000'); // Corrigido para 3000 (mesmo que useReactionGame.js)
  const { toast } = useToast();

  const handleSubmit = () => {
    // Validate all parameters
    const distanceValidation = validateNumericParameter(distancethreshold, 10, 500, 'Distância');
    const globalTimeoutValidation = validateNumericParameter(globaltimeout, 100, 10000, 'Timeout Global');
    const lastResortValidation = validateNumericParameter(lastresorttimeout, 100, 5000, 'Timeout Last Resort');

    if (!distanceValidation.isValid) {
      toast({ title: "Erro", description: distanceValidation.error, variant: "destructive" });
      return;
    }
    if (!globalTimeoutValidation.isValid) {
      toast({ title: "Erro", description: globalTimeoutValidation.error, variant: "destructive" });
      return;
    }
    if (!lastResortValidation.isValid) {
      toast({ title: "Erro", description: lastResortValidation.error, variant: "destructive" });
      return;
    }

    onConfigure({
      distancethreshold: distanceValidation.value,
      globaltimeout: globalTimeoutValidation.value,
      lastresorttimeout: lastResortValidation.value,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Configurar Parâmetros dos Dispositivos</DialogTitle>
          <DialogDescription>
            Estes valores serão enviados para todos os dispositivos ativos.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="distancethreshold" className="text-right">
              Distância (cm)
            </Label>
            <Input
              id="distancethreshold"
              type="number"
              value={distancethreshold}
              onChange={(e) => setDistancethreshold(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="globaltimeout" className="text-right">
              Timeout Global (ms)
            </Label>
            <Input
              id="globaltimeout"
              type="number"
              value={globaltimeout}
              onChange={(e) => setGlobaltimeout(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="lastresorttimeout" className="text-right">
              Timeout Final (ms)
            </Label>
            <Input
              id="lastresorttimeout"
              type="number"
              value={lastresorttimeout}
              onChange={(e) => setLastresorttimeout(e.target.value)}
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button onClick={handleSubmit}>Enviar Configuração</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ConfigureDevicesModal;