import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, <PERSON>lt<PERSON>, Legend, ResponsiveContainer, CartesianGrid } from 'recharts';
import { <PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react';

const RunDetailModal = ({ run, isOpen, onOpenChange }) => {
  if (!run) return null;

  const calculateSegmentData = () => {
    const { timestamps, distance } = run;
    const sortedTimestamps = [...timestamps].sort((a, b) => a - b);

    const timeSegment1 = (sortedTimestamps[1] - sortedTimestamps[0]) / 1000;
    const timeSegment2 = (sortedTimestamps[2] - sortedTimestamps[1]) / 1000;

    const segmentDistance = distance / 2;

    const speedKmhSegment1 = timeSegment1 > 0 ? (segmentDistance / timeSegment1) * 3.6 : 0;
    const speedKmhSegment2 = timeSegment2 > 0 ? (segmentDistance / timeSegment2) * 3.6 : 0;

    return [
      { name: 'Dispositivo 1 ➔ 2', velocidade: speedKmhSegment1.toFixed(2), tempo: timeSegment1.toFixed(3) },
      { name: 'Dispositivo 2 ➔ 3', velocidade: speedKmhSegment2.toFixed(2), tempo: timeSegment2.toFixed(3) },
    ];
  };

  const chartData = calculateSegmentData();
  const fasterSegment = parseFloat(chartData[0].velocidade) > parseFloat(chartData[1].velocidade) ? 'primeiro' : 'segundo';

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="bg-secondary border-border text-foreground max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl text-primary flex items-center gap-2">
            <Zap />
            Análise Detalhada da Corrida
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Comparação de desempenho entre os trechos da corrida de {run.participantName} em {new Date(run.date).toLocaleString('pt-BR')}.
          </DialogDescription>
        </DialogHeader>
        <div className="my-4">
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
              <XAxis dataKey="name" stroke="hsl(var(--muted-foreground))" />
              <YAxis stroke="hsl(var(--muted-foreground))" label={{ value: 'km/h', angle: -90, position: 'insideLeft', fill: 'hsl(var(--muted-foreground))' }} />
              <Tooltip
                cursor={{ fill: 'hsl(var(--accent))' }}
                contentStyle={{
                  background: 'hsl(var(--background))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: 'var(--radius)',
                  color: 'hsl(var(--foreground))',
                }}
              />
              <Legend wrapperStyle={{ color: 'hsl(var(--foreground))' }} />
              <Bar dataKey="velocidade" name="Velocidade (km/h)" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
        <div className="text-center p-4 bg-background/50 rounded-md">
          <p className="text-lg flex items-center justify-center gap-2">
            {fasterSegment === 'primeiro' ? <Rabbit className="text-green-400" /> : <Turtle className="text-red-400" />}
            O participante foi mais rápido no <span className="font-bold text-primary">{fasterSegment} trecho</span>.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RunDetailModal;