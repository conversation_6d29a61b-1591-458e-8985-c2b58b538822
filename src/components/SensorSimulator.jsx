import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Zap, Target, Play } from 'lucide-react';

const SensorSimulator = ({ devices, publishMessage, isConnected }) => {
  const [simulatingDevice, setSimulatingDevice] = useState(null);

  const simulateSensorActivation = (device) => {
    if (!isConnected) {
      return;
    }

    setSimulatingDevice(device.mac);
    
    // Simular telemetria do sensor
    const telemetryTopic = `/Module/Telemetry/${device.mac}`;
    const telemetryPayload = JSON.stringify({
      pass: "true",
      timestamp: Date.now().toString()
    });

    console.log('🔬 Simulating sensor activation:', { telemetryTopic, telemetryPayload });
    
    publishMessage(telemetryTopic, telemetryPayload);

    // Reset visual feedback
    setTimeout(() => {
      setSimulatingDevice(null);
    }, 1000);
  };

  const activeDevices = devices.filter(d => d.active);

  if (!isConnected) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Target className="w-8 h-8 mx-auto text-muted-foreground mb-2" />
          <p className="text-muted-foreground text-sm">
            Conecte ao MQTT para simular sensores
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="w-5 h-5" />
          Simulador de Sensores
        </CardTitle>
        <CardDescription>
          Simule ativação de sensores para testar o jogo de reação
        </CardDescription>
      </CardHeader>
      <CardContent>
        {activeDevices.length === 0 ? (
          <div className="text-center py-4">
            <p className="text-muted-foreground text-sm">
              Nenhum dispositivo ativo para simular
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="text-sm font-medium mb-3">
              Clique para simular ativação:
            </div>
            {activeDevices.map((device) => (
              <div key={device.mac} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="flex flex-col">
                    <span className="font-medium">{device.name}</span>
                    <span className="text-xs text-muted-foreground">{device.mac}</span>
                  </div>
                  <Badge variant={device.status === 'online' ? 'default' : 'secondary'}>
                    {device.status}
                  </Badge>
                </div>
                <Button
                  size="sm"
                  onClick={() => simulateSensorActivation(device)}
                  disabled={simulatingDevice === device.mac}
                  className={simulatingDevice === device.mac ? 'bg-green-500' : ''}
                >
                  {simulatingDevice === device.mac ? (
                    <>
                      <Zap className="w-4 h-4 mr-2 animate-pulse" />
                      Ativado!
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Simular
                    </>
                  )}
                </Button>
              </div>
            ))}
            
            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-2">📡 Telemetria Simulada</h5>
              <div className="text-xs text-blue-800 dark:text-blue-200">
                <div><strong>Tópico:</strong> <code>/Module/Telemetry/[MAC]</code></div>
                <div><strong>Payload:</strong> <code>{"{"}"pass":"true","timestamp":"[TIMESTAMP]"{"}"}</code></div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SensorSimulator;
