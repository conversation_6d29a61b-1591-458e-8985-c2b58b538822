import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Settings, Play, RotateCcw, ChevronsUpDown, Check, TestTube2, UserPlus } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

import { cn } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { useDebounce } from '@/hooks/useDebounce';
import { validateDistance, validateParticipantName } from '@/lib/validation';

const ControlPanel = ({
  distance,
  setDistance,
  participantName,
  setParticipantName,
  participants,
  addParticipant,
  isRunning,
  startMeasurement,
  resetMeasurement,
  mqttStatus,
}) => {
  const isConnected = mqttStatus === 'Connected';
  const [open, setOpen] = React.useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = React.useState(false);
  const [newParticipant, setNewParticipant] = React.useState('');
  const [searchTerm, setSearchTerm] = React.useState('');
  const [distanceError, setDistanceError] = React.useState('');
  const participantNames = React.useMemo(() => Object.keys(participants), [participants]);

  const filteredParticipants = React.useMemo(() => {
    if (!searchTerm) return participantNames;
    return participantNames.filter(name =>
      name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [participantNames, searchTerm]);

  // Fechar dropdown quando clicar fora
  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (open && !event.target.closest('.participant-selector-container')) {
        setOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [open]);
  const { toast } = useToast();

  // Debounce distance input for validation
  const debouncedDistance = useDebounce(distance, 500);

  // Validate distance when it changes
  useEffect(() => {
    if (debouncedDistance) {
      const validation = validateDistance(debouncedDistance);
      setDistanceError(validation.isValid ? '' : validation.error);
    } else {
      setDistanceError('');
    }
  }, [debouncedDistance]);

  const handleCreateParticipant = () => {
    const nameValidation = validateParticipantName(newParticipant);

    if (!nameValidation.isValid) {
      toast({
        title: 'Erro',
        description: nameValidation.error,
        variant: 'destructive',
      });
      return;
    }

    if (participantNames.includes(nameValidation.value)) {
      toast({
        title: 'Erro',
        description: `Participante "${nameValidation.value}" já existe.`,
        variant: 'destructive',
      });
      return;
    }

    addParticipant(nameValidation.value);
    setParticipantName(nameValidation.value);
    setNewParticipant('');
    setIsAddModalOpen(false);
    setOpen(false);
    toast({
      title: 'Participante Criado!',
      description: `${nameValidation.value} foi adicionado e selecionado.`,
    });
  };
  
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleCreateParticipant();
    }
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="bg-secondary border-border h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="text-primary" />
              Painel de Controlo
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              Ajuste os parâmetros da medição e inicie.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
                <motion.div key="step-2" initial={{opacity: 0}} animate={{opacity: 1}} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="distance">Distância (metros)</Label>
                    <Input
                      id="distance"
                      type="number"
                      placeholder="Ex: 20"
                      value={distance}
                      onChange={(e) => setDistance(e.target.value)}
                      disabled={isRunning}
                      className={distanceError ? 'border-red-500' : ''}
                    />
                    {distanceError && (
                      <p className="text-sm text-red-500">{distanceError}</p>
                    )}
                  </div>
                  {/* Seleção de Participante - Componente Customizado */}
                  <div className="space-y-2">
                    <Label>Nome do Participante</Label>
                    <div className="relative participant-selector-container">
                      <Button
                        variant="outline"
                        className="w-full justify-between"
                        disabled={isRunning}
                        onClick={() => setOpen(!open)}
                      >
                        {participantName || "Selecione um participante..."}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>

                      {open && (
                        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-popover border rounded-md shadow-lg">
                          {/* Campo de busca */}
                          <div className="p-2 border-b">
                            <input
                              type="text"
                              placeholder="Procure um nome..."
                              className="w-full px-3 py-2 text-sm bg-background border rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              autoFocus
                            />
                          </div>

                          {/* Lista de participantes */}
                          <div
                            className="participant-list-custom"
                            style={{
                              maxHeight: filteredParticipants.length > 6 ? '240px' : 'auto',
                              overflowY: filteredParticipants.length > 6 ? 'auto' : 'visible'
                            }}
                          >
                            {filteredParticipants.length === 0 ? (
                              <div className="p-3 text-sm text-muted-foreground text-center">
                                Nenhum participante encontrado.
                              </div>
                            ) : (
                              filteredParticipants.map((name) => (
                                <div
                                  key={name}
                                  className="flex items-center px-3 py-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground transition-colors"
                                  onClick={() => {
                                    setParticipantName(name);
                                    setOpen(false);
                                    setSearchTerm('');
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      participantName === name ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                  {name}
                                </div>
                              ))
                            )}
                          </div>

                          {/* Botão adicionar participante */}
                          <div className="p-1 border-t">
                            <Button
                              variant="ghost"
                              className="w-full"
                              onClick={() => {
                                setOpen(false);
                                setIsAddModalOpen(true);
                                setSearchTerm('');
                              }}
                            >
                              <UserPlus className="mr-2 h-4 w-4" />
                              Adicionar Participante
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-3 pt-2">
                    <Button
                      onClick={() => startMeasurement(false)}
                      disabled={isRunning || !isConnected || !participantName || distanceError}
                      className="flex-1 bg-primary text-primary-foreground hover:bg-primary/90 rounded-full"
                    >
                      <Play className="w-4 h-4 mr-2" />
                      Iniciar
                    </Button>
                    <Button
                      onClick={() => startMeasurement(true)}
                      disabled={isRunning || !participantName || distanceError}
                      variant="outline"
                      className="flex-1 rounded-full"
                    >
                      <TestTube2 className="w-4 h-4 mr-2" />
                      Simular
                    </Button>
                    <Button
                      onClick={resetMeasurement}
                      variant="secondary"
                      className="text-foreground rounded-full"
                    >
                      <RotateCcw className="w-4 h-4" />
                    </Button>
                  </div>
                </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Novo Participante</DialogTitle>
            <DialogDescription>
              Insira o nome do novo participante. Este nome será usado para registrar as medições.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Nome
              </Label>
              <Input
                id="name"
                value={newParticipant}
                onChange={(e) => setNewParticipant(e.target.value)}
                className="col-span-3"
                onKeyDown={handleKeyDown}
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleCreateParticipant}>Salvar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ControlPanel;