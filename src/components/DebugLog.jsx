import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Trash2, Server } from 'lucide-react';

const DebugLog = ({ logs, onClear }) => {
  const scrollRef = React.useRef(null);

  React.useEffect(() => {
    if (scrollRef.current) {
      // Scroll to the bottom to show the latest log
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [logs]);

  return (
    <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }}>
      <Card className="bg-secondary/50 border-border">
        <CardHeader className="flex flex-row items-center justify-between py-4">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Server className="text-primary w-5 h-5"/>
              Log de Telemetria
            </CardTitle>
            <CardDescription className="text-muted-foreground pt-1">
              Mensagens recebidas do broker MQTT.
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" onClick={onClear}>
            <Trash2 className="w-4 h-4 mr-2" />
            Limpar Log
          </Button>
        </CardHeader>
        <CardContent>
          <div ref={scrollRef} className="bg-background/50 p-4 rounded-lg h-64 overflow-y-auto font-mono text-xs border border-border/50 flex flex-col">
            {logs.length === 0 ? (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <p>Aguardando mensagens MQTT...</p>
              </div>
            ) : (
              logs.map((log, index) => (
                <div key={`${log.id}-${index}`} className="mb-2 last:mb-0 flex">
                  <span className="text-primary/70 mr-3">[{new Date(log.id).toLocaleTimeString()}]</span>
                  <div className="flex-grow">
                    <span className="text-yellow-400 font-semibold mr-2">{log.topic}</span>
                    <span className="text-foreground whitespace-pre-wrap break-all">{log.message}</span>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default DebugLog;