import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Play, Square, RotateCcw, Settings, ChevronsUpDown, Check, UserPlus, Eye, CheckCircle, XCircle, Save, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';
import { validateMacAddress } from '@/lib/validation';
import { validateParticipantName } from '@/lib/validation';

const ReactionGame = ({
  gameState,
  currentRound,
  totalRounds,
  gameTime,
  maxGameTime,
  reactionTimeout,
  setReactionTimeout,
  nextActivationDelay,
  setNextActivationDelay,
  allowSameDevice,
  setAllowSameDevice,
  distance,
  setDistance,
  setTotalRounds,
  setMaxGameTime,
  activeDevice,
  waitingForReaction,
  successes,
  misses,
  reactions,
  accuracy,
  averageReactionTime,
  maxSpeed,
  startGame,
  stopGame,
  resetGame,
  saveGameManually,
  clearCorruptedData,
  debugLocalStorage,
  mqttStatus,
  // Configurações de dispositivos
  distanceThreshold,
  setDistanceThreshold,
  globalTimeout,
  setGlobalTimeout,
  lastResortTimeout,
  setLastResortTimeout,
  configureDevices,
  // Novos props para participantes
  participantName,
  setParticipantName,
  participants,
  addParticipant,
  setAppMode,
  // Props para dispositivos
  devices,
  toggleDeviceActive,
  isConnectedProp,
  addDevice,
  updateDevice,
  publishMessage
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const [open, setOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newParticipant, setNewParticipant] = useState('');
  const [configLastUpdated, setConfigLastUpdated] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Estados para modais de dispositivos
  const [isAddDeviceModalOpen, setIsAddDeviceModalOpen] = useState(false);
  const [isConfigDeviceModalOpen, setIsConfigDeviceModalOpen] = useState(false);
  const [newDeviceMac, setNewDeviceMac] = useState('');
  const { toast } = useToast();

  const isRunning = gameState === 'running';
  const isFinished = gameState === 'finished';



  const participantNames = React.useMemo(() => {
    const names = Object.keys(participants);
    console.log('🧑‍🤝‍🧑 ReactionGame participantNames:', { participants, names, participantNamesLength: names.length });
    return names;
  }, [participants]);

  const filteredParticipants = React.useMemo(() => {
    if (!searchTerm) return participantNames;
    return participantNames.filter(name =>
      name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [participantNames, searchTerm]);

  // Fechar dropdown quando clicar fora
  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (open && !event.target.closest('.participant-selector-container')) {
        setOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [open]);



  // Monitor mudanças nas configurações para forçar re-render
  useEffect(() => {
    const timestamp = new Date().toLocaleTimeString();
    setConfigLastUpdated(timestamp);
    console.log('🎮 ReactionGame - Configurações atualizadas em', timestamp, ':', {
      totalRounds,
      maxGameTime,
      distance,
      nextActivationDelay,
      allowSameDevice,
      distanceThreshold,
      globalTimeout,
      lastResortTimeout
    });
  }, [totalRounds, maxGameTime, distance, nextActivationDelay, allowSameDevice, distanceThreshold, globalTimeout, lastResortTimeout]);

  const handleCreateParticipant = () => {
    const nameValidation = validateParticipantName(newParticipant);

    if (!nameValidation.isValid) {
      toast({
        title: 'Erro',
        description: nameValidation.error,
        variant: 'destructive',
      });
      return;
    }

    if (participantNames.includes(nameValidation.value)) {
      toast({
        title: 'Erro',
        description: `Participante "${nameValidation.value}" já existe.`,
        variant: 'destructive',
      });
      return;
    }

    // Chamar addParticipant - ele já faz validação interna mas não faz mal
    addParticipant(nameValidation.value);

    // Se chegou aqui, o participante foi criado com sucesso
    setParticipantName(nameValidation.value);
    setNewParticipant('');
    setIsAddModalOpen(false);
    setOpen(false);

    // Toast adicional para confirmar seleção
    toast({
      title: 'Participante Selecionado!',
      description: `${nameValidation.value} foi selecionado para o jogo de reação.`,
    });
  };
  
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  const getGameProgress = () => {
    if (totalRounds > 0) {
      return (currentRound / totalRounds) * 100;
    }
    return (gameTime / maxGameTime) * 100;
  };

  // Função para adicionar dispositivo
  const handleAddDevice = () => {
    const macValidation = validateMacAddress(newDeviceMac);

    if (!macValidation.isValid) {
      toast({ title: "Erro", description: macValidation.error, variant: "destructive" });
      return;
    }

    addDevice({ mac: macValidation.value, name: '' });
    setNewDeviceMac('');
    setIsAddDeviceModalOpen(false);
    toast({ title: "Sucesso", description: "Dispositivo adicionado com sucesso!" });
  };

  // Função para configurar dispositivos
  const handleConfigureDevices = () => {
    const activeDevices = devices.filter(device => device.active);

    if (activeDevices.length === 0) {
      toast({
        title: "Aviso",
        description: "Nenhum dispositivo ativo para configurar.",
        variant: "destructive"
      });
      return;
    }

    activeDevices.forEach(device => {
      const config = {
        command: "configure",
        parameters: {
          distancethreshold: distanceThreshold,
          globaltimeout: globalTimeout,
          lastresorttimeout: lastResortTimeout
        }
      };

      const topic = `/Module/Command/${device.mac}`;
      const payload = JSON.stringify(config);
      publishMessage(topic, payload);
    });

    setIsConfigDeviceModalOpen(false);
    toast({
      title: "Configurações Enviadas",
      description: `Configurações enviadas para ${activeDevices.length} dispositivo(s) ativo(s).`
    });
  };

  // Função para obter ícone de status do dispositivo
  const getDeviceStatusIcon = (device) => {
    if (device.active) {
      return <CheckCircle className="w-5 h-5 text-primary" />;
    } else {
      return <XCircle className="w-5 h-5 text-muted-foreground" />;
    }
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-6">
      <div className="max-w-7xl mx-auto space-y-6">

        {/* Game Progress Panel - Top */}
        <div className="bg-secondary rounded-lg p-6 space-y-6 border border-border">
          {/* Header Principal - Nome do Participante */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-primary uppercase">
              {participantName || "Nome"}
            </h1>

            {/* Ícone Central */}
            <div className="flex justify-center py-6">
              <div className="flex items-center justify-center">
                <span className="material-symbols-rounded text-primary" style={{ fontSize: '72px' }}>detection_and_zone</span>
              </div>
            </div>
          </div>

          {/* Métricas Principais */}
          <div className="grid grid-cols-5 gap-6">
            {/* Sucessos */}
            <div className="border-2 border-border/50 rounded-md p-3 bg-background/30 text-center space-y-2">
              <div className="text-sm text-muted-foreground">Sucesso</div>
              <div className="text-2xl font-semibold text-foreground">{successes || 0}</div>
            </div>

            {/* Faltas */}
            <div className="border-2 border-border/50 rounded-md p-3 bg-background/30 text-center space-y-2">
              <div className="text-sm text-muted-foreground">Faltas</div>
              <div className="text-2xl font-semibold text-foreground">{misses || 0}</div>
            </div>

            {/* Precisão */}
            <div className="border-2 border-border/50 rounded-md p-3 bg-background/30 text-center space-y-2">
              <div className="text-sm text-muted-foreground">Precisão</div>
              <div className="text-2xl font-semibold text-foreground">{(accuracy || 0).toFixed(1)}%</div>
            </div>

            {/* Tempo Médio */}
            <div className="border-2 border-border/50 rounded-md p-3 bg-background/30 text-center space-y-2">
              <div className="text-sm text-muted-foreground">Tempo Médio</div>
              <div className="text-2xl font-semibold text-foreground">{Math.round(averageReactionTime || 0)} ms</div>
            </div>

            {/* Velocidade Máxima */}
            <div className="border-2 border-border/50 rounded-md p-3 bg-background/30 text-center space-y-2">
              <div className="text-sm text-muted-foreground">Velocidade Máxima</div>
              <div className="text-2xl font-semibold text-foreground">{(maxSpeed || 5.3).toFixed(1)} km/h</div>
            </div>
          </div>

          {/* Barra de Progresso */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Eye className="w-5 h-5 text-muted-foreground" />
                <span className="text-lg font-semibold text-foreground">Progresso</span>
              </div>
              <div className="text-sm text-muted-foreground">
                {gameTime ? `${Math.floor(gameTime / 1000 / 60)}:${String(Math.floor((gameTime / 1000) % 60)).padStart(2, '0')}` : '0:00'} / {maxGameTime ? `${Math.floor(maxGameTime / 60)}:${String(maxGameTime % 60).padStart(2, '0')}` : '5:00'}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm text-foreground">
                <span>Ronda {currentRound} de {totalRounds}</span>
                <span>{getGameProgress().toFixed(1)}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getGameProgress()}%` }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Two Column Layout - Controls and Devices */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Controles Panel - Left */}
          <div className="bg-black rounded-lg p-6 border border-border">
            <div className="flex items-center gap-2 mb-6">
              <Settings className="w-5 h-5 text-primary" />
              <h2 className="text-xl font-semibold text-foreground">Painel de Controlo</h2>
            </div>

            <div className="space-y-4">
              {/* Seleção de Participante */}
              <div className="space-y-2">
                <Label className="text-foreground">Participante</Label>
                <div className="relative participant-selector-container">
                  <Button
                    variant="outline"
                    className="w-full justify-between"
                    disabled={isRunning}
                    onClick={() => setOpen(!open)}
                  >
                    {participantName || "Selecionar participante..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>

                  {open && (
                    <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-popover border border-border rounded-md shadow-lg">
                      {/* Campo de busca */}
                      <div className="p-2 border-b border-border">
                        <input
                          type="text"
                          placeholder="Procurar participante..."
                          className="w-full px-3 py-2 text-sm bg-input border border-border text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          autoFocus
                        />
                      </div>

                      {/* Lista de participantes */}
                      <div
                        className="participant-list-custom"
                        style={{
                          maxHeight: filteredParticipants.length > 6 ? '240px' : 'auto',
                          overflowY: filteredParticipants.length > 6 ? 'auto' : 'visible'
                        }}
                      >
                        {filteredParticipants.length === 0 ? (
                          <div className="p-3 text-sm text-muted-foreground text-center">
                            Nenhum participante encontrado.
                          </div>
                        ) : (
                          filteredParticipants.map((name) => (
                            <div
                              key={name}
                              className="flex items-center px-3 py-2 text-sm cursor-pointer hover:bg-accent text-foreground transition-colors"
                              onClick={() => {
                                setParticipantName(name);
                                setOpen(false);
                                setSearchTerm('');
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4 text-orange-500",
                                  participantName === name ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {name}
                            </div>
                          ))
                        )}
                      </div>

                      {/* Botão adicionar participante */}
                      <div className="p-1 border-t border-border">
                        <Button
                          variant="ghost"
                          className="w-full"
                          onClick={() => {
                            setOpen(false);
                            setIsAddModalOpen(true);
                            setSearchTerm('');
                          }}
                        >
                          <UserPlus className="mr-2 h-4 w-4" />
                          Adicionar Participante
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {!isRunning ? (
                <Button
                  onClick={() => {
                    console.log('🎮 Start Game button clicked', { isConnectedProp, participantName });
                    startGame(setAppMode);
                  }}
                  disabled={!isConnectedProp || !participantName}
                  className="w-full"
                  size="lg"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Iniciar Jogo
                </Button>
              ) : (
                <Button
                  onClick={stopGame}
                  variant="destructive"
                  className="w-full"
                  size="lg"
                >
                  <Square className="w-4 h-4 mr-2" />
                  Parar Jogo
                </Button>
              )}

              <Button
                onClick={resetGame}
                variant="outline"
                className="w-full"
                disabled={isRunning}
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset
              </Button>

              {/* Botões de Configuração lado a lado */}
              <div className="flex gap-2">
                <Button
                  onClick={() => setShowSettings(!showSettings)}
                  variant="outline"
                  className="flex-1"
                  disabled={isRunning}
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Configurações de Jogo
                </Button>

                <Button
                  onClick={() => {
                    // Forçar salvamento de todas as configurações
                    console.log('💾 Force saving all settings...');

                    // Configurações do jogo
                    const gameSettings = {
                      totalRounds,
                      maxGameTime,
                      distance,
                      nextActivationDelay,
                      allowSameDevice
                    };

                    // Configurações dos dispositivos
                    const deviceSettings = {
                      distanceThreshold,
                      globalTimeout,
                      lastResortTimeout
                    };

                    console.log('🎮 Current game settings:', gameSettings);
                    console.log('📡 Current device settings:', deviceSettings);

                    toast({
                      title: "✅ Configurações Salvas!",
                      description: `Jogo: ${totalRounds} rondas, ${maxGameTime}s | Dispositivos: ${distanceThreshold}cm, ${globalTimeout}ms`
                    });
                  }}
                  variant="outline"
                  className="flex-1"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Salvar Configurações
                </Button>
              </div>

              <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded border border-border">
                ℹ️ Configurações são salvas automaticamente quando alteradas
              </div>
            </div>
          </div>

          {/* Dispositivos Panel - Right */}
          <div className="bg-secondary rounded-lg p-6 border border-border">
            <div className="flex items-center gap-2 mb-6">
              <Settings className="w-5 h-5 text-primary" />
              <h2 className="text-xl font-semibold text-foreground">Dispositivos</h2>
            </div>
            <p className="text-muted-foreground text-sm mb-4">Gestão e status dos módulos de medição.</p>

            {/* Device List */}
            <div className="space-y-3">
              {devices && devices.length > 0 ? (
                devices.map((device) => (
                  <div key={device.mac} className="flex items-center justify-between p-3 rounded-lg" style={{ backgroundColor: 'hsl(var(--background))' }}>
                    <div className="flex items-center gap-3">
                      {getDeviceStatusIcon(device)}
                      <div>
                        <div className="text-foreground font-medium">{device.name || `Sensor ${device.mac.slice(-4)}`}</div>
                        <div className="text-muted-foreground text-xs">{device.mac}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className={`text-sm ${device.active ? 'text-primary' : 'text-muted-foreground'}`}>
                        {device.active ? 'Ativo' : 'Inativo'}
                      </span>
                      <Switch
                        id={`switch-${device.mac}`}
                        checked={device.active}
                        onCheckedChange={() => toggleDeviceActive(device.mac)}
                        disabled={!isConnectedProp}
                        aria-label={`Ativar/Desativar dispositivo ${device.mac}`}
                      />
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-muted-foreground py-4">
                  <p>Nenhum dispositivo encontrado</p>
                  <p className="text-xs mt-1">Conecte-se ao MQTT para ver os dispositivos</p>
                </div>
              )}
            </div>

            {/* Device Action Buttons */}
            <div className="flex justify-end gap-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setIsAddDeviceModalOpen(true)}
                disabled={!isConnectedProp}
              >
                <Plus className="w-4 h-4 mr-2" />
                Adicionar
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsConfigDeviceModalOpen(true)}
              >
                <Settings className="w-4 h-4 mr-2" />
                Configurações
              </Button>
            </div>
          </div>
        </div>

        {/* Settings Panel */}
        {showSettings && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-secondary rounded-lg p-6 border border-border"
          >
            <h3 className="text-xl font-semibold text-foreground mb-4">Configurações do Jogo</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="rounds">Número de Rondas</Label>
                <Input
                  id="rounds"
                  type="number"
                  min="1"
                  max="100"
                  value={totalRounds}
                  onChange={(e) => setTotalRounds(parseInt(e.target.value) || 10)}
                />
              </div>

              <div>
                <Label htmlFor="maxTime">Tempo Máximo (s)</Label>
                <Input
                  id="maxTime"
                  type="number"
                  min="60"
                  max="1800"
                  value={maxGameTime}
                  onChange={(e) => setMaxGameTime(parseInt(e.target.value) || 300)}
                />
              </div>

              <div>
                <Label htmlFor="distance">Distância (m)</Label>
                <Input
                  id="distance"
                  type="number"
                  min="0"
                  max="50"
                  step="0.1"
                  value={distance}
                  onChange={(e) => setDistance(parseFloat(e.target.value) || 20)}
                />
              </div>

              <div>
                <Label htmlFor="delay">Delay (ms)</Label>
                <Input
                  id="delay"
                  type="number"
                  min="0"
                  max="3000"
                  value={nextActivationDelay}
                  onChange={(e) => setNextActivationDelay(parseInt(e.target.value) || 1000)}
                />
              </div>
            </div>

            {/* Configuração de Seleção de Dispositivos */}
            <div className="mt-6 pt-4 border-t border-border">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="allowSameDevice" className="text-sm font-medium">
                    Permitir Reativar o Mesmo Dispositivo
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Se ativado, o mesmo sensor pode ser ativado consecutivamente
                  </p>
                </div>
                <Switch
                  id="allowSameDevice"
                  checked={allowSameDevice}
                  onCheckedChange={setAllowSameDevice}
                  disabled={isRunning}
                />
              </div>
            </div>
          </motion.div>
        )}

        {/* Modal para Adicionar Participante */}
        <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Adicionar Novo Participante</DialogTitle>
              <DialogDescription>
                Digite o nome do novo participante para o jogo de reação.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="newParticipant" className="text-white">Nome do Participante</Label>
                <Input
                  id="newParticipant"
                  value={newParticipant}
                  onChange={(e) => setNewParticipant(e.target.value)}
                  placeholder="Digite o nome..."
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleCreateParticipant();
                    }
                  }}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsAddModalOpen(false)}
              >
                Cancelar
              </Button>
              <Button
                onClick={handleCreateParticipant}
              >
                Adicionar
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Modal para Adicionar Dispositivo */}
        <Dialog open={isAddDeviceModalOpen} onOpenChange={setIsAddDeviceModalOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Adicionar Novo Dispositivo</DialogTitle>
              <DialogDescription>
                Insira o endereço MAC do novo dispositivo para registá-lo no sistema.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="newDeviceMac" className="text-white">MAC Address</Label>
                <Input
                  id="newDeviceMac"
                  value={newDeviceMac}
                  onChange={(e) => setNewDeviceMac(e.target.value.toUpperCase())}
                  placeholder="XX:XX:XX:XX:XX:XX"
                  className="font-mono"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleAddDevice();
                    }
                  }}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsAddDeviceModalOpen(false)}
              >
                Cancelar
              </Button>
              <Button
                onClick={handleAddDevice}
              >
                Adicionar Dispositivo
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Modal para Configurações de Dispositivos */}
        <Dialog open={isConfigDeviceModalOpen} onOpenChange={setIsConfigDeviceModalOpen}>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>⚙️ Configurações de Dispositivos</DialogTitle>
              <DialogDescription>
                Configurações enviadas via MQTT para todos os sensores ativos
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="modal-distanceThreshold">Alcance do Dispositivo (cm)</Label>
                <Input
                  id="modal-distanceThreshold"
                  type="number"
                  min="30"
                  max="200"
                  value={distanceThreshold}
                  onChange={(e) => setDistanceThreshold(parseInt(e.target.value) || 150)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Distância máxima de detecção do sensor (30-200cm)
                </p>
              </div>

              <div>
                <Label htmlFor="modal-globalTimeout">Timeout Global (ms)</Label>
                <Input
                  id="modal-globalTimeout"
                  type="number"
                  min="500"
                  max="10000"
                  value={globalTimeout}
                  onChange={(e) => setGlobalTimeout(parseInt(e.target.value) || 5000)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Tempo máximo que o LED fica aceso (500-10000ms)
                </p>
              </div>

              <div>
                <Label htmlFor="modal-lastResortTimeout">Timeout Final (ms)</Label>
                <Input
                  id="modal-lastResortTimeout"
                  type="number"
                  min="1500"
                  max="5000"
                  value={lastResortTimeout}
                  onChange={(e) => setLastResortTimeout(parseInt(e.target.value) || 3000)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Tempo de piscar antes do timeout (1500-5000ms)
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsConfigDeviceModalOpen(false)}
              >
                Cancelar
              </Button>
              <Button
                onClick={handleConfigureDevices}
              >
                Enviar Configuração
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default ReactionGame;
