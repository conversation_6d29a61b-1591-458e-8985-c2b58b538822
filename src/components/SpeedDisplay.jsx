import React, { memo } from 'react';
import { motion, useMotionValue, useTransform, animate } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';

const SpeedDisplay = ({ measurement, bestRun, participantName: selectedParticipant }) => {
  const speed = measurement ? measurement.speedKmh : 0;
  const bestSpeed = bestRun ? bestRun.speedKmh : 0;
  const participantName = measurement ? measurement.participantName : (selectedParticipant || 'Aguardando...');
  const duration = measurement ? measurement.totalTime.toFixed(3) : '0.000';
  const distance = measurement ? measurement.distance : '0';

  const radius = 140;
  const circumference = 2 * Math.PI * radius;
  const semiCircumference = circumference / 2;
  const maxSpeed = 60; // Max speed for the gauge
  
  const speedProgress = Math.min(speed, maxSpeed) / maxSpeed;
  const strokeDashoffset = semiCircumference - speedProgress * semiCircumference;

  const bestSpeedProgress = Math.min(bestSpeed, maxSpeed) / maxSpeed;
  const bestStrokeDashoffset = semiCircumference - bestSpeedProgress * semiCircumference;

  const count = useMotionValue(0);
  const [integerPart, setIntegerPart] = React.useState('0');
  const [decimalPart, setDecimalPart] = React.useState('00');

  // Atualizar as partes quando o valor muda
  React.useEffect(() => {
    const unsubscribe = count.onChange((latest) => {
      const formatted = latest.toFixed(2);
      const parts = formatted.split('.');
      setIntegerPart(parts[0] || '0');
      setDecimalPart(parts[1] || '00');
    });

    return unsubscribe;
  }, [count]);

  React.useEffect(() => {
    const controls = animate(count, speed, {
      duration: 1.5,
      ease: "circOut",
    });
    return () => {
        controls.stop();
        if (speed === 0) {
            count.set(0);
        }
    };
  }, [speed, count]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ type: 'spring', stiffness: 100, delay: 0.1 }}
    >
      <Card className="bg-secondary border-border overflow-hidden rounded-xl">
        <CardContent className="p-6 flex flex-col items-center justify-center">
          <h3 className="text-3xl font-bold text-primary truncate max-w-full mb-2">{participantName}</h3>
          <div className="relative w-[320px] h-[170px]">
            <svg className="w-full h-full" viewBox="0 0 300 150">
              <path
                d={`M ${150 - radius},150 A ${radius},${radius} 0 0 1 ${150 + radius},150`}
                stroke="hsl(var(--border))"
                strokeWidth="20"
                fill="transparent"
                strokeLinecap="butt"
              />
              {bestSpeed > 0 && (
                 <path
                  d={`M ${150 - radius},150 A ${radius},${radius} 0 0 1 ${150 + radius},150`}
                  stroke="hsl(var(--primary) / 0.3)"
                  strokeWidth="20"
                  fill="transparent"
                  strokeLinecap="butt"
                  strokeDasharray={semiCircumference}
                  strokeDashoffset={bestStrokeDashoffset}
                />
              )}
               <motion.path
                  d={`M ${150 - radius},150 A ${radius},${radius} 0 0 1 ${150 + radius},150`}
                  stroke="hsl(var(--primary))"
                  strokeWidth="20"
                  fill="transparent"
                  strokeLinecap="butt"
                  strokeDasharray={semiCircumference}
                  initial={{ strokeDashoffset: semiCircumference }}
                  animate={{ strokeDashoffset }}
                  transition={{ duration: 1.5, ease: "circOut" }}
                />
            </svg>
            <div className="absolute inset-0 flex flex-col items-center justify-end pb-2">
              <div className="flex items-baseline">
                <motion.span className="text-7xl font-bold text-foreground italic">
                  {integerPart}
                </motion.span>
                <motion.span className="text-4xl font-bold text-foreground italic">
                  .{decimalPart}
                </motion.span>
              </div>
              <span className="text-lg text-muted-foreground -mt-2">km/h</span>
            </div>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-4 text-center w-full max-w-sm">
            <div className="border-2 border-border/50 rounded-md p-3 bg-background/30">
              <p className="text-sm text-muted-foreground">Duração (s)</p>
              <p className="text-2xl font-semibold">{duration}</p>
            </div>
            <div className="border-2 border-border/50 rounded-md p-3 bg-background/30">
              <p className="text-sm text-muted-foreground">Distância (m)</p>
              <p className="text-2xl font-semibold">{distance}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default memo(SpeedDisplay);