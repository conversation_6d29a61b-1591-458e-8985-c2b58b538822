import React from 'react';
import { motion } from 'framer-motion';
import { Zap, Target, Timer, Gauge } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const ModeSelector = ({ currentMode, onModeChange }) => {
  const modes = [
    {
      id: 'speed',
      title: 'Modo Velocidade',
      description: 'Mede a velocidade entre múltiplos sensores numa distância conhecida',
      icon: Gauge,
      color: 'bg-blue-500',
      features: [
        'Medição de velocidade precisa',
        'Múltiplos sensores em linha',
        'Cálculo automático km/h',
        'Histórico de performances'
      ]
    },
    {
      id: 'reaction',
      title: 'Modo Reação',
      description: 'Jogo de reação com ativação aleatória de sensores e contagem de tempo',
      icon: Target,
      color: 'bg-green-500',
      features: [
        'Ativação aleatória de sensores',
        'Tempo de reação medido',
        'Contador de sucessos/faltas',
        'Configuração de timeouts'
      ]
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      {modes.map((mode) => {
        const Icon = mode.icon;
        const isSelected = currentMode === mode.id;
        
        return (
          <motion.div
            key={mode.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: mode.id === 'speed' ? 0 : 0.1 }}
          >
            <Card
              className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                isSelected
                  ? 'ring-2 ring-primary shadow-lg scale-105'
                  : 'hover:scale-102'
              }`}
              style={{
                '--tw-shadow-color': 'hsl(240, 4%, 26%)',
                '--tw-shadow': 'var(--tw-shadow-colored)'
              }}
              onClick={() => onModeChange(mode.id)}
            >
              <CardHeader className="text-center">
                <div className={`w-16 h-16 ${mode.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  <Icon className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="flex items-center justify-center gap-2">
                  {mode.title}
                  {isSelected && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="w-3 h-3 bg-primary rounded-full"
                    />
                  )}
                </CardTitle>
                <CardDescription>{mode.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {mode.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                      {feature}
                    </li>
                  ))}
                </ul>
                {isSelected && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="mt-4"
                  >
                    <Button className="w-full" variant="default">
                      <Timer className="w-4 h-4 mr-2" />
                      Modo Ativo
                    </Button>
                  </motion.div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        );
      })}
    </div>
  );
};

export default ModeSelector;
