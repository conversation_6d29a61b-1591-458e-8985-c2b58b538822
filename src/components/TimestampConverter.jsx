import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowRight, Timer, Wind } from 'lucide-react';

const TimestampConverter = ({ distance }) => {
  const [ts1, setTs1] = useState('');
  const [ts2, setTs2] = useState('');

  const analysis = useMemo(() => {
    const timestamp1 = Number(ts1);
    const timestamp2 = Number(ts2);
    const dist = parseFloat(distance);

    if (!timestamp1 || !timestamp2 || isNaN(timestamp1) || isNaN(timestamp2) || !dist) {
      return null;
    }

    const firstTs = Math.min(timestamp1, timestamp2);
    const lastTs = Math.max(timestamp1, timestamp2);

    const date1 = new Date(firstTs).toLocaleString('pt-PT', { timeZone: 'UTC' });
    const date2 = new Date(lastTs).toLocaleString('pt-PT', { timeZone: 'UTC' });

    const durationSeconds = (lastTs - firstTs) / 1000.0;
    
    if (durationSeconds <= 0) {
      return { date1, date2, duration: 0, speedKmh: 'Inválido' };
    }

    const speedMps = dist / durationSeconds;
    const speedKmh = speedMps * 3.6;

    return {
      date1,
      date2,
      duration: durationSeconds.toFixed(4),
      speedKmh: speedKmh.toFixed(2),
    };
  }, [ts1, ts2, distance]);

  return (
    <Card className="bg-card/50 backdrop-blur-sm">
      <CardHeader>
        <CardTitle>Analisador de Telemetria</CardTitle>
        <CardDescription>Cole os timestamps das telemetrias para analisar a duração e a velocidade.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
          <div>
            <Label htmlFor="ts1">Timestamp 1</Label>
            <Input id="ts1" value={ts1} onChange={(e) => setTs1(e.target.value)} placeholder="Ex: 1751983583815" />
          </div>
          <div>
            <Label htmlFor="ts2">Timestamp 2</Label>
            <Input id="ts2" value={ts2} onChange={(e) => setTs2(e.target.value)} placeholder="Ex: 1751983584352" />
          </div>
        </div>
        
        {analysis && (
          <div className="space-y-4 rounded-lg border bg-background/50 p-4">
            <h3 className="font-semibold text-center text-lg">Resultados da Análise</h3>
            <div className="flex flex-col md:flex-row justify-around items-center text-center gap-4">
              <div className="flex flex-col items-center">
                <p className="text-sm text-muted-foreground">Início</p>
                <p className="font-mono text-lg">{analysis.date1}</p>
              </div>
              <ArrowRight className="hidden md:block h-6 w-6 text-muted-foreground" />
              <div className="flex flex-col items-center">
                <p className="text-sm text-muted-foreground">Fim</p>
                <p className="font-mono text-lg">{analysis.date2}</p>
              </div>
            </div>
            <div className="flex justify-around pt-4 border-t">
              <div className="flex items-center gap-3 text-xl">
                <Timer className="h-6 w-6 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Duração</p>
                  <p className="font-bold">{analysis.duration} s</p>
                </div>
              </div>
              <div className="flex items-center gap-3 text-xl">
                <Wind className="h-6 w-6 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Velocidade</p>
                  <p className="font-bold">{analysis.speedKmh} km/h</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TimestampConverter;