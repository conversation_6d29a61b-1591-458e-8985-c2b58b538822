import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, User, FileDown } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';

const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const gameData = payload[0].payload;
    return (
      <div
        className="p-3 border shadow-lg rounded-md"
        style={{
          backgroundColor: 'hsl(240, 4%, 26%)',
          borderColor: 'hsl(240, 4%, 36%)',
          '--tw-shadow-color': 'hsl(240, 4%, 26%)',
          '--tw-shadow': 'var(--tw-shadow-colored)'
        }}
      >
        <p className="font-semibold" style={{ color: 'hsl(240, 4%, 90%)' }}>{`${gameData.name}`}</p>
        <p className="text-orange-400">{`Precisão: ${gameData.precisao.toFixed(1)}%`}</p>
        <p style={{ color: 'hsl(240, 4%, 85%)' }}>{`Tempo Médio: ${gameData.tempoMedio.toFixed(0)}ms`}</p>
        <p className="text-xs" style={{ color: 'hsl(240, 4%, 70%)' }}>{gameData.date}</p>
      </div>
    );
  }
  return null;
};

const ReactionCharts = ({ participants }) => {
  const [selectedParticipant, setSelectedParticipant] = useState('all');
  const { toast } = useToast();

  const chartData = useMemo(() => {
    const participantKeys = Object.keys(participants);
    if(participantKeys.length === 0) return [];

    if (selectedParticipant === 'all') {
      const allGames = participantKeys.flatMap(name => 
        (participants[name].reactionGames || []).map(game => ({ ...game, name }))
      ).sort((a, b) => new Date(a.date) - new Date(b.date));
      
      return allGames.slice(-10).map(game => ({
        name: game.name.slice(0, 10) + (game.name.length > 10 ? '...' : ''),
        precisao: game.accuracy,
        tempoMedio: game.averageReactionTime,
        sucessos: game.successes,
        falhas: game.misses,
        date: new Date(game.date).toLocaleDateString('pt-BR'),
      }));
    } else {
      const participantGames = participants[selectedParticipant]?.reactionGames || [];
      return participantGames
        .sort((a, b) => b.accuracy - a.accuracy) // Sort by best accuracy
        .slice(0, 10) // Take top 10
        .map((game, index) => ({
          name: `Jogo ${index + 1}`,
          precisao: game.accuracy,
          tempoMedio: game.averageReactionTime,
          sucessos: game.successes,
          falhas: game.misses,
          date: new Date(game.date).toLocaleDateString('pt-BR'),
        }));
    }
  }, [participants, selectedParticipant]);



  const exportChartData = () => {
    if (chartData.length === 0) {
      toast({ title: 'Nenhum dado para exportar', variant: 'destructive' });
      return;
    }

    const csvContent = [
      ['Participante', 'Precisão (%)', 'Tempo Médio (ms)', 'Sucessos', 'Falhas', 'Data'].join(','),
      ...chartData.map(row => [
        row.name,
        row.precisao.toFixed(1),
        row.tempoMedio.toFixed(0),
        row.sucessos,
        row.falhas,
        row.date
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `graficos-reacao-${selectedParticipant}-${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({ title: 'Dados dos gráficos exportados com sucesso!' });
  };

  const participantOptions = Object.keys(participants).filter(name => 
    participants[name].reactionGames && participants[name].reactionGames.length > 0
  );

  if (participantOptions.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-6 w-6 text-primary" />
              Gráficos de Reação
            </CardTitle>
            <CardDescription>
              Análise visual dos jogos de reação dos participantes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-center py-8">
              Nenhum jogo de reação registrado. Jogue alguns jogos para ver os gráficos.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-6 w-6 text-primary" />
                Gráficos de Reação
              </CardTitle>
              <CardDescription>
                Análise visual dos jogos de reação dos participantes
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Select value={selectedParticipant} onValueChange={setSelectedParticipant}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Selecionar participante" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Participantes</SelectItem>
                  {participantOptions.map(name => (
                    <SelectItem key={name} value={name}>
                      <User className="h-4 w-4 mr-2 inline" />
                      {name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button onClick={exportChartData} variant="outline" size="sm">
                <FileDown className="h-4 w-4 mr-2" />
                Exportar
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Gráfico de Precisão */}
            <div>
              <h4 className="text-center text-muted-foreground mb-2">Precisão (%)</h4>
              <ResponsiveContainer width="100%" height={250}>
                <LineChart data={chartData}>
                  <XAxis dataKey="name" stroke="hsl(var(--muted-foreground))" fontSize={12} tickLine={false} axisLine={false} />
                  <YAxis domain={[0, 100]} stroke="hsl(var(--muted-foreground))" fontSize={12} tickLine={false} axisLine={false} />
                  <Tooltip
                    content={<CustomTooltip />}
                    cursor={{ stroke: 'hsl(240, 4%, 26%)', strokeWidth: 2 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="precisao"
                    stroke="hsl(var(--primary))"
                    strokeWidth={2}
                    dot={{ r: 4, fill: 'hsl(var(--primary))' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Gráfico de Tempo de Reação */}
            <div>
              <h4 className="text-center text-muted-foreground mb-2">Tempo Médio (ms)</h4>
              <ResponsiveContainer width="100%" height={250}>
                <BarChart data={chartData}>
                  <XAxis dataKey="name" stroke="hsl(var(--muted-foreground))" fontSize={12} tickLine={false} axisLine={false} />
                  <YAxis stroke="hsl(var(--muted-foreground))" fontSize={12} tickLine={false} axisLine={false} />
                  <Tooltip
                    content={<CustomTooltip />}
                    cursor={{ fill: 'hsl(240, 4%, 26%)', opacity: 0.3 }}
                  />
                  <Bar dataKey="tempoMedio" fill="hsl(var(--primary))" opacity={0.5} radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>


          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ReactionCharts;
