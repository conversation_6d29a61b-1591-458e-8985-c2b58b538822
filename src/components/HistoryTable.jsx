import React, { useMemo, useState } from 'react';
import { motion } from 'framer-motion';
import { History, PlusCircle, BarChart2, FileDown, UserPlus, Edit } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { exportToCSV } from '@/lib/utils';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const ParticipantEditModal = ({ isOpen, onOpenChange, participant, onSave, allNames }) => {
  const [name, setName] = useState('');
  const [age, setAge] = useState('');
  const [photo, setPhoto] = useState('');
  const { toast } = useToast();

  React.useEffect(() => {
    if (participant) {
      setName(participant.name);
      setAge(participant.age || '');
      setPhoto(participant.photo || '');
    }
  }, [participant]);

  const handleSave = () => {
    const newName = name.trim();
    if (!newName) {
      toast({ title: "Erro", description: "O nome não pode estar em branco.", variant: "destructive" });
      return;
    }
    if (newName !== participant.name && allNames.includes(newName)) {
      toast({ title: "Erro", description: `O nome "${newName}" já existe.`, variant: "destructive" });
      return;
    }
    onSave(participant.name, newName, age, photo);
    onOpenChange(false);
  };

  if (!participant) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Editar Participante</DialogTitle>
          <DialogDescription>
            Altere os dados de {participant.name}.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">Nome</Label>
            <Input id="name" value={name} onChange={(e) => setName(e.target.value)} className="col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="age" className="text-right">Idade</Label>
            <Input id="age" type="number" value={age} onChange={(e) => setAge(e.target.value)} className="col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="photo" className="text-right">URL da Foto</Label>
            <Input id="photo" value={photo} onChange={(e) => setPhoto(e.target.value)} className="col-span-3" />
          </div>
        </div>
        <DialogFooter>
          <Button onClick={handleSave}>Salvar Alterações</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};


const HistoryTable = ({ participants, setParticipantName, onSelectRun, updateParticipant }) => {
  const { toast } = useToast();
  const [editingParticipant, setEditingParticipant] = useState(null);

  const sortedParticipants = useMemo(() => {
    return Object.entries(participants)
      .map(([name, data]) => {
        const { runs, reactionGames, ...details } = data;

        // Processar runs de velocidade
        let bestRun = null;
        let latestRunDate = null;
        if (runs && runs.length > 0) {
          bestRun = runs.reduce((best, current) => (current.speedKmh > (best.speedKmh || 0) ? current : best), runs[0]);
          latestRunDate = new Date(Math.max(...runs.map(run => new Date(run.date))));
        }

        // Processar jogos de reação
        let bestReactionGame = null;
        let latestReactionDate = null;
        if (reactionGames && reactionGames.length > 0) {
          bestReactionGame = reactionGames.reduce((best, current) => (current.accuracy > (best.accuracy || 0) ? current : best), reactionGames[0]);
          latestReactionDate = new Date(Math.max(...reactionGames.map(game => new Date(game.date))));
        }

        // Determinar a data mais recente entre runs e reactionGames
        let latestDate = null;
        if (latestRunDate && latestReactionDate) {
          latestDate = latestRunDate > latestReactionDate ? latestRunDate : latestReactionDate;
        } else if (latestRunDate) {
          latestDate = latestRunDate;
        } else if (latestReactionDate) {
          latestDate = latestReactionDate;
        }

        return {
          name,
          runs: runs || [],
          reactionGames: reactionGames || [],
          details,
          bestRun,
          bestReactionGame,
          latestDate
        };
      })
      .sort((a, b) => {
        if (!b.latestDate) return -1;
        if (!a.latestDate) return 1;
        return b.latestDate - a.latestDate;
      });
  }, [participants]);

  const handleNewRun = (name) => {
    setParticipantName(name);
    toast({
      title: `Pronto para ${name}!`,
      description: "Clique em 'Iniciar' ou 'Simular' para uma nova medição.",
    });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleExport = (participantName, runs) => {
    if (!runs || runs.length === 0) {
      toast({ title: 'Nenhum dado para exportar.', variant: 'destructive' });
      return;
    }

    const dataToExport = runs.map(run => ({
      Participante: run.participantName,
      Data: new Date(run.date).toLocaleString('pt-BR'),
      Distancia_m: run.distance,
      Tempo_s: run.totalTime.toFixed(3),
      Velocidade_kmh: run.speedKmh.toFixed(2),
    }));

    const filename = `historico_${participantName.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`;
    exportToCSV(dataToExport, filename);
    toast({
      title: 'Exportação Concluída!',
      description: `O ficheiro ${filename} foi descarregado.`,
    });
  };
  
  const handleExportAll = () => {
    const allRuns = Object.values(participants).flatMap(p => p.runs || []);
    const allReactionGames = Object.values(participants).flatMap(p => p.reactionGames || []);

    if (allRuns.length === 0 && allReactionGames.length === 0) {
      toast({ title: 'Nenhum dado para exportar.', variant: 'destructive' });
      return;
    }

    // Exportar runs se existirem
    if (allRuns.length > 0) {
      handleExport('todos_participantes_velocidade', allRuns);
    }

    // Exportar jogos de reação se existirem
    if (allReactionGames.length > 0) {
      handleExport('todos_participantes_reacao', allReactionGames);
    }
  };
  
  const handleEditParticipant = (participant) => {
    setEditingParticipant({name: participant.name, ...participant.details});
  }

  return (
    <>
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5 }}>
        <Card className="bg-secondary border-border">
          <CardHeader className="flex-row justify-between items-center">
            <div>
              <CardTitle className="flex items-center gap-2"><History className="text-primary" />Histórico</CardTitle>
              <CardDescription>Registro de medições por participante.</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={handleExportAll} variant="outline" size="sm" disabled={Object.keys(participants).length === 0}>
                <FileDown className="h-4 w-4 mr-2" />
                Exportar Tudo
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {Object.keys(participants).length === 0 ? (
              <p className="text-muted-foreground text-center py-8">Nenhum participante registrado. Adicione um para começar.</p>
            ) : (
              <Accordion type="single" collapsible className="w-full">
                {sortedParticipants.map(({ name, runs, reactionGames, details, bestRun, bestReactionGame }) => (
                  <AccordionItem value={name} key={name}>
                    <AccordionTrigger className="hover:bg-white/5 px-4 rounded-md">
                      <div className="flex justify-between items-center w-full">
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage src={details.photo} alt={name} />
                            <AvatarFallback>{name.substring(0, 2).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <div>
                            <span className="font-bold text-lg text-foreground">{name}</span>
                            {details.age && <p className="text-sm text-muted-foreground">{details.age} anos</p>}
                          </div>
                        </div>

                        <div className="flex items-center gap-2 text-sm">
                          {bestRun ? (
                            <span className="hidden sm:inline text-muted-foreground">Melhor: <span className="font-semibold text-primary">{bestRun.speedKmh.toFixed(2)} km/h</span></span>
                          ) : bestReactionGame ? (
                            <span className="hidden sm:inline text-muted-foreground">Melhor: <span className="font-semibold text-primary">{bestReactionGame.accuracy.toFixed(1)}%</span></span>
                          ) : (
                            <span className="hidden sm:inline text-muted-foreground">Sem dados</span>
                          )}
                          <span className="hidden md:inline text-muted-foreground">
                            Corridas: <span className="font-semibold text-primary">{runs.length}</span>
                            {reactionGames.length > 0 && (
                              <> | Reação: <span className="font-semibold text-primary">{reactionGames.length}</span></>
                            )}
                          </span>
                          <Button size="icon" variant="ghost" onClick={(e) => { e.stopPropagation(); handleEditParticipant({name, details}); }}>
                            <Edit className="h-5 w-5 text-yellow-400" />
                          </Button>
                          <Button size="icon" variant="ghost" onClick={(e) => {
                            e.stopPropagation();
                            if (runs.length > 0) handleExport(name + '_velocidade', runs);
                            if (reactionGames.length > 0) handleExport(name + '_reacao', reactionGames);
                          }} disabled={runs.length === 0 && reactionGames.length === 0}>
                            <FileDown className="h-5 w-5 text-blue-400" />
                          </Button>
                          <Button size="icon" variant="ghost" onClick={(e) => { e.stopPropagation(); handleNewRun(name); }}>
                            <PlusCircle className="h-5 w-5 text-primary" />
                          </Button>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-6">
                        {/* Dados de Velocidade */}
                        {runs && runs.length > 0 && (
                          <div>
                            <h4 className="font-semibold text-lg mb-3 text-blue-600 dark:text-blue-400">🏃‍♂️ Corridas de Velocidade ({runs.length})</h4>
                            <div className="overflow-x-auto">
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    <TableHead>Data</TableHead>
                                    <TableHead className="text-center">Distância (m)</TableHead>
                                    <TableHead className="text-center">Tempo (s)</TableHead>
                                    <TableHead className="text-center">Velocidade (km/h)</TableHead>
                                    <TableHead className="text-right">Ações</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {runs.sort((a, b) => new Date(b.date) - new Date(a.date)).map((run) => (
                                    <TableRow key={run.id} className="border-b-0">
                                      <TableCell>{new Date(run.date).toLocaleString('pt-BR')}</TableCell>
                                      <TableCell className="text-center">{run.distance}</TableCell>
                                      <TableCell className="text-center">{run.totalTime.toFixed(3)}</TableCell>
                                      <TableCell className="text-center font-bold text-primary">{run.speedKmh.toFixed(2)}</TableCell>
                                      <TableCell className="text-right">
                                        <Button variant="outline" size="sm" onClick={() => onSelectRun(run)}>
                                          <BarChart2 className="h-4 w-4 mr-2" />
                                          Ver Detalhes
                                        </Button>
                                      </TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          </div>
                        )}

                        {/* Dados de Reação */}
                        {reactionGames && reactionGames.length > 0 && (
                          <div>
                            <h4 className="font-semibold text-lg mb-3 text-green-600 dark:text-green-400">🎯 Jogos de Reação ({reactionGames.length})</h4>
                            <div className="overflow-x-auto">
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    <TableHead>Data</TableHead>
                                    <TableHead className="text-center">Rondas</TableHead>
                                    <TableHead className="text-center">Sucessos</TableHead>
                                    <TableHead className="text-center">Falhas</TableHead>
                                    <TableHead className="text-center">Precisão (%)</TableHead>
                                    <TableHead className="text-center">Tempo Médio (ms)</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {reactionGames.sort((a, b) => new Date(b.date) - new Date(a.date)).map((game) => (
                                    <TableRow key={game.id} className="border-b-0">
                                      <TableCell>{new Date(game.date).toLocaleString('pt-BR')}</TableCell>
                                      <TableCell className="text-center">{game.totalRounds}</TableCell>
                                      <TableCell className="text-center text-green-600 font-semibold">{game.successes}</TableCell>
                                      <TableCell className="text-center text-red-600 font-semibold">{game.misses}</TableCell>
                                      <TableCell className="text-center font-bold text-primary">{game.accuracy.toFixed(1)}%</TableCell>
                                      <TableCell className="text-center">{game.averageReactionTime.toFixed(0)}</TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          </div>
                        )}

                        {/* Mensagem quando não há dados */}
                        {(!runs || runs.length === 0) && (!reactionGames || reactionGames.length === 0) && (
                          <p className="text-muted-foreground text-center p-4">Nenhuma medição para este participante.</p>
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </CardContent>
        </Card>
      </motion.div>
      
      <ParticipantEditModal
        isOpen={!!editingParticipant}
        onOpenChange={() => setEditingParticipant(null)}
        participant={editingParticipant}
        onSave={updateParticipant}
        allNames={Object.keys(participants)}
      />
    </>
  );
};

export default HistoryTable;