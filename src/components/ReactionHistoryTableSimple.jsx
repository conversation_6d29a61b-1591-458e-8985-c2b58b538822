import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Target } from 'lucide-react';

const ReactionHistoryTableSimple = ({ participants = {} }) => {
  return (
    <div className="space-y-6">
      <Card className="bg-secondary border-border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="text-primary" />
            Histórico de Jogos de Reação
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>Histórico temporariamente simplificado para debug</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReactionHistoryTableSimple;
