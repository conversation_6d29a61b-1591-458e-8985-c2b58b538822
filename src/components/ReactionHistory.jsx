import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { History, Target, Clock, TrendingUp, Download, Eye } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const ReactionHistory = ({ participants, participantName }) => {
  const [selectedGame, setSelectedGame] = useState(null);

  // Obter jogos de reação do participante atual
  const reactionGames = participantName && participants[participantName]
    ? participants[participantName].reactionGames || []
    : [];

  // Debug logs
  console.log('📊 ReactionHistory debug:', {
    participantName,
    participants,
    participantData: participants[participantName],
    reactionGames,
    reactionGamesCount: reactionGames.length
  });

  // Obter estatísticas gerais
  const getParticipantStats = () => {
    if (reactionGames.length === 0) return null;

    const totalGames = reactionGames.length;
    const totalSuccesses = reactionGames.reduce((sum, game) => sum + game.successes, 0);
    const totalMisses = reactionGames.reduce((sum, game) => sum + game.misses, 0);
    const avgAccuracy = reactionGames.reduce((sum, game) => sum + game.accuracy, 0) / totalGames;
    const avgReactionTime = reactionGames.reduce((sum, game) => sum + game.averageReactionTime, 0) / totalGames;
    const bestAccuracy = Math.max(...reactionGames.map(game => game.accuracy));
    const fastestReaction = Math.min(...reactionGames.map(game => game.averageReactionTime));

    return {
      totalGames,
      totalSuccesses,
      totalMisses,
      avgAccuracy,
      avgReactionTime,
      bestAccuracy,
      fastestReaction
    };
  };

  const stats = getParticipantStats();

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getAccuracyColor = (accuracy) => {
    if (accuracy >= 80) return { style: { backgroundColor: '#f39523', color: 'white' } }; // Rápido/Excelente (≥80%)
    if (accuracy >= 60) return { style: { backgroundColor: '#d4851f', color: 'white' } }; // Médio/Bom (60-79%)
    return { style: { backgroundColor: '#805118', color: 'white' } }; // Lento/Baixo (<60%)
  };

  if (!participantName) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Target className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground">Selecione um participante para ver o histórico de jogos de reação.</p>
        </CardContent>
      </Card>
    );
  }

  if (reactionGames.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <History className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground">
            {participantName} ainda não jogou nenhum jogo de reação.
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Complete um jogo de reação para ver as estatísticas aqui.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="w-5 h-5" />
            Histórico de Reação - {participantName}
          </CardTitle>
          <CardDescription>
            {stats.totalGames} jogos realizados
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="stats" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="stats">Estatísticas</TabsTrigger>
          <TabsTrigger value="history">Histórico</TabsTrigger>
        </TabsList>

        <TabsContent value="stats" className="space-y-4">
          {/* Estatísticas Gerais */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.totalGames}</div>
                <div className="text-sm text-muted-foreground">Jogos</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{stats.avgAccuracy.toFixed(1)}%</div>
                <div className="text-sm text-muted-foreground">Precisão Média</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.avgReactionTime.toFixed(0)}ms</div>
                <div className="text-sm text-muted-foreground">Tempo Médio</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-orange-600">{stats.bestAccuracy.toFixed(1)}%</div>
                <div className="text-sm text-muted-foreground">Melhor Precisão</div>
              </CardContent>
            </Card>
          </div>

          {/* Recordes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Recordes Pessoais
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-lg font-bold text-green-600">{stats.bestAccuracy.toFixed(1)}%</div>
                  <div className="text-sm text-muted-foreground">Melhor Precisão</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-lg font-bold text-blue-600">{stats.fastestReaction.toFixed(0)}ms</div>
                  <div className="text-sm text-muted-foreground">Reação Mais Rápida</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-lg font-bold text-purple-600">{stats.totalSuccesses}</div>
                  <div className="text-sm text-muted-foreground">Total de Sucessos</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          {/* Lista de Jogos */}
          <div className="space-y-3">
            {reactionGames.slice().reverse().map((game, index) => (
              <motion.div
                key={game.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card
                  className="transition-shadow cursor-pointer hover:shadow-lg"
                  style={{
                    '--tw-shadow-color': 'hsl(240, 4%, 26%)',
                    '--tw-shadow': 'var(--tw-shadow-colored)'
                  }}
                  onClick={() => setSelectedGame(selectedGame === game.id ? null : game.id)}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="text-sm text-muted-foreground">
                          {formatDate(game.date)}
                        </div>
                        <Badge
                          className={getAccuracyColor(game.accuracy).className || ''}
                          style={getAccuracyColor(game.accuracy).style || {}}
                        >
                          {game.accuracy.toFixed(1)}% precisão
                        </Badge>
                        <Badge variant="outline">
                          {game.averageReactionTime.toFixed(0)}ms médio
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {game.successes}/{game.successes + game.misses} sucessos
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {game.totalRounds} rondas
                        </div>
                      </div>
                    </div>

                    {selectedGame === game.id && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="mt-4 pt-4 border-t space-y-2"
                      >
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Tempo de Jogo:</span>
                            <div>{Math.floor(game.gameTime / 60)}:{(game.gameTime % 60).toString().padStart(2, '0')}</div>
                          </div>
                          <div>
                            <span className="font-medium">Sucessos:</span>
                            <div className="text-green-600">{game.successes}</div>
                          </div>
                          <div>
                            <span className="font-medium">Faltas:</span>
                            <div className="text-red-600">{game.misses}</div>
                          </div>
                          <div>
                            <span className="font-medium">Vel. Máxima:</span>
                            <div>{game.maxSpeed.toFixed(1)} km/h</div>
                          </div>
                        </div>
                        
                        <div className="text-xs text-muted-foreground">
                          <strong>Configurações:</strong> Timeout: {game.settings?.reactionTimeout}ms, 
                          Delay: {game.settings?.nextActivationDelay}ms, 
                          Distância: {game.settings?.distance}m
                        </div>
                      </motion.div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ReactionHistory;
