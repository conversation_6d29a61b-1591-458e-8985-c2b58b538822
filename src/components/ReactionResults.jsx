import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Download, FileText, BarChart3, Clock, Target, Zap } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

const ReactionResults = ({ reactions, gameStats, participantName }) => {
  const [exportFormat, setExportFormat] = useState('csv');

  const exportData = (format) => {
    if (!reactions || reactions.length === 0) {
      return;
    }

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `reaction-game-${participantName || 'unknown'}-${timestamp}`;

    if (format === 'csv') {
      exportCSV(filename);
    } else if (format === 'json') {
      exportJSON(filename);
    }
  };

  const exportCSV = (filename) => {
    // Calcular estatísticas adicionais
    const successfulReactions = reactions.filter(r => r.success);
    const reactionTimes = successfulReactions.map(r => r.reactionTime);
    const speeds = reactions.map(r => r.speed).filter(s => s > 0);

    // Calcular mediana
    const sortedTimes = [...reactionTimes].sort((a, b) => a - b);
    const median = sortedTimes.length > 0
      ? sortedTimes.length % 2 === 0
        ? (sortedTimes[sortedTimes.length / 2 - 1] + sortedTimes[sortedTimes.length / 2]) / 2
        : sortedTimes[Math.floor(sortedTimes.length / 2)]
      : 0;

    // Calcular velocidade média
    const averageSpeed = speeds.length > 0 ? speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length : 0;

    // Seção 1: Análise do Jogo
    const analysisSection = [
      '=== ANÁLISE DO JOGO ===',
      'Métrica,Valor',
      `Participante,${participantName || 'Desconhecido'}`,
      `Data do Jogo,${new Date().toLocaleString('pt-BR')}`,
      `Total de Rondas,${gameStats.totalRounds}`,
      `Sucessos,${gameStats.successes}`,
      `Falhas,${gameStats.misses}`,
      `Precisão (%),${gameStats.accuracy.toFixed(1)}`,
      `Tempo Médio de Reação (ms),${gameStats.averageReactionTime.toFixed(0)}`,
      `Melhor Tempo (ms),${gameStats.bestReactionTime.toFixed(0)}`,
      `Pior Tempo (ms),${gameStats.worstReactionTime.toFixed(0)}`,
      `Tempo Total de Jogo (s),${gameStats.gameTime ? gameStats.gameTime.toFixed(1) : 'N/A'}`,
      '',
      '=== ANÁLISE DE TEMPO ===',
      `Tempo mais rápido:,${gameStats.bestReactionTime.toFixed(0)}ms`,
      `Tempo mais lento:,${gameStats.worstReactionTime.toFixed(0)}ms`,
      `Mediana:,${median.toFixed(0)}ms`,
      '',
      '=== ANÁLISE DE VELOCIDADE ===',
      `Velocidade máxima:,${gameStats.maxSpeed.toFixed(1)} km/h`,
      `Velocidade média:,${averageSpeed.toFixed(1)} km/h`,
      `Reações com velocidade:,${speeds.length}`,
      '',
      ''
    ];

    // Seção 2: Detalhes das Reações
    const detailsSection = [
      '=== DETALHES DAS REAÇÕES ===',
      'Ronda,Dispositivo,Tempo_Reacao_ms,Sucesso,Velocidade_kmh,Data_Hora',
      ...reactions.map(reaction => [
        reaction.round,
        reaction.device.name || reaction.device.mac,
        reaction.reactionTime,
        reaction.success ? 'Sim' : 'Não',
        reaction.speed.toFixed(2),
        reaction.timestamp ? new Date(reaction.timestamp).toLocaleString('pt-BR') : 'N/A'
      ].join(','))
    ];

    const csvContent = [
      ...analysisSection,
      ...detailsSection
    ].join('\n');

    downloadFile(csvContent, `${filename}.csv`, 'text/csv');
  };

  const exportJSON = (filename) => {
    const exportData = {
      metadata: {
        participant: participantName || 'Desconhecido',
        exportDate: new Date().toISOString(),
        gameType: 'reaction',
        version: '1.0'
      },
      gameStats: {
        totalReactions: reactions.length,
        successes: gameStats.successes,
        misses: gameStats.misses,
        accuracy: gameStats.accuracy,
        averageReactionTime: gameStats.averageReactionTime,
        maxSpeed: gameStats.maxSpeed,
        totalGameTime: gameStats.gameTime
      },
      reactions: reactions.map(reaction => ({
        round: reaction.round,
        device: {
          name: reaction.device.name,
          mac: reaction.device.mac
        },
        activationTime: reaction.activationTime,
        reactionTime: reaction.reactionTime,
        success: reaction.success,
        speed: reaction.speed,
        timestamp: reaction.timestamp
      }))
    };

    const jsonContent = JSON.stringify(exportData, null, 2);
    downloadFile(jsonContent, `${filename}.json`, 'application/json');
  };

  const downloadFile = (content, filename, mimeType) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const getReactionColor = (reactionTime, success) => {
    if (!success) return { className: 'bg-gray-600 text-gray-100 dark:bg-gray-800 dark:text-gray-300' }; // Cinza escuro - Falha
    if (reactionTime < 500) return { style: { backgroundColor: '#f39523', color: 'white' } }; // Rápido - #f39523
    if (reactionTime < 1000) return { style: { backgroundColor: '#d4851f', color: 'white' } }; // Médio - Entre rápido e lento
    return { style: { backgroundColor: '#805118', color: 'white' } }; // Lento - #805118
  };

  const formatTime = (ms) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  if (reactions.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Target className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground">Nenhuma reação registada ainda.</p>
          <p className="text-sm text-muted-foreground mt-2">
            Inicie um jogo para ver os resultados aqui.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com Exportação */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Resultados do Jogo de Reação
              </CardTitle>
              <CardDescription>
                {reactions.length} reações registadas
                {participantName && ` para ${participantName}`}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => exportData('csv')}
                variant="outline"
                size="sm"
                disabled={!reactions || reactions.length === 0}
              >
                <FileText className="w-4 h-4 mr-2" />
                CSV
              </Button>
              <Button
                onClick={() => exportData('json')}
                variant="outline"
                size="sm"
                disabled={!reactions || reactions.length === 0}
              >
                <Download className="w-4 h-4 mr-2" />
                JSON
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <Tabs defaultValue="analysis" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="details">Detalhes</TabsTrigger>
          <TabsTrigger value="analysis">Análise</TabsTrigger>
        </TabsList>



        <TabsContent value="details" className="space-y-4">
          {/* Tabela Detalhada */}
          <Card>
            <CardHeader>
              <CardTitle>Todas as Reações</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Ronda</th>
                      <th className="text-left p-2">Dispositivo</th>
                      <th className="text-left p-2">Tempo Reação</th>
                      <th className="text-left p-2">Velocidade</th>
                      <th className="text-left p-2">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reactions.map((reaction) => (
                      <tr key={reaction.id} className="border-b hover:bg-muted/50">
                        <td className="p-2">{reaction.round}</td>
                        <td className="p-2">{reaction.device.name || reaction.device.mac}</td>
                        <td className="p-2">
                          {reaction.success ? formatTime(reaction.reactionTime) : '-'}
                        </td>
                        <td className="p-2">
                          {reaction.success && reaction.speed > 0 
                            ? `${reaction.speed.toFixed(1)} km/h` 
                            : '-'
                          }
                        </td>
                        <td className="p-2">
                          <Badge
                            className={getReactionColor(reaction.reactionTime, reaction.success).className || ''}
                            style={getReactionColor(reaction.reactionTime, reaction.success).style || {}}
                          >
                            {reaction.success ? 'Sucesso' : 'Falta'}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          {/* Análise de Performance */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Análise de Tempo
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Tempo mais rápido:</span>
                    <span className="font-medium">
                      {Math.min(...reactions.filter(r => r.success).map(r => r.reactionTime))}ms
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tempo mais lento:</span>
                    <span className="font-medium">
                      {Math.max(...reactions.filter(r => r.success).map(r => r.reactionTime))}ms
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Mediana:</span>
                    <span className="font-medium">
                      {reactions.filter(r => r.success).length > 0 
                        ? reactions.filter(r => r.success)
                            .map(r => r.reactionTime)
                            .sort((a, b) => a - b)[Math.floor(reactions.filter(r => r.success).length / 2)]
                        : 0
                      }ms
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  Análise de Velocidade
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Velocidade máxima:</span>
                    <span className="font-medium">
                      {gameStats.maxSpeed.toFixed(1)} km/h
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Velocidade média:</span>
                    <span className="font-medium">
                      {reactions.filter(r => r.success && r.speed > 0).length > 0
                        ? (reactions.filter(r => r.success && r.speed > 0)
                            .reduce((sum, r) => sum + r.speed, 0) / 
                           reactions.filter(r => r.success && r.speed > 0).length).toFixed(1)
                        : 0
                      } km/h
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Reações com velocidade:</span>
                    <span className="font-medium">
                      {reactions.filter(r => r.success && r.speed > 0).length}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ReactionResults;
