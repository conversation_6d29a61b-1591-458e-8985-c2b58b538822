import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { MapPin, CheckCircle, XCircle, AlertTriangle, Plus, Settings, Edit } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { validateMacAddress } from '@/lib/validation';

const EditDeviceModal = ({ isOpen, onOpenChange, device, onSave }) => {
  const [name, setName] = useState('');
  const { toast } = useToast();

  React.useEffect(() => {
    if (device) {
      setName(device.name || '');
    }
  }, [device]);

  const handleSave = () => {
    onSave(device.mac, name);
    onOpenChange(false);
    toast({ title: "Sucesso", description: "Nome do dispositivo atualizado." });
  };
  
  if (!device) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Editar Dispositivo</DialogTitle>
          <DialogDescription>
            Atribua um nome personalizado ao dispositivo com o MAC {device.mac}.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">Nome</Label>
            <Input 
              id="name" 
              value={name} 
              onChange={(e) => setName(e.target.value)} 
              className="col-span-3"
              placeholder="Ex: Sensor Partida" 
            />
          </div>
        </div>
        <DialogFooter>
          <Button onClick={handleSave}>Salvar Alterações</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const DevicePanel = ({
  devices,
  toggleDeviceActive,
  isConnected,
  addDevice,
  updateDevice,
  onConfigure,
  // Configurações de Dispositivos
  distanceThreshold,
  setDistanceThreshold,
  globalTimeout,
  setGlobalTimeout,
  lastResortTimeout,
  setLastResortTimeout,
  publishMessage
}) => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newDeviceMac, setNewDeviceMac] = useState('');
  const [editingDevice, setEditingDevice] = useState(null);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
  const { toast } = useToast();

  // Função local para enviar configurações de dispositivos
  const handleConfigureDevices = () => {
    console.log('🔧 handleConfigureDevices called');
    console.log('📡 publishMessage available:', !!publishMessage);
    console.log('🔌 isConnected:', isConnected);

    const activeDevices = devices.filter(d => d.active);
    console.log('📱 Active devices:', activeDevices.length);

    if (activeDevices.length === 0) {
      toast({ title: "Erro", description: "Nenhum dispositivo ativo!", variant: "destructive" });
      return;
    }

    if (!publishMessage) {
      console.error('❌ publishMessage is not available');
      toast({ title: "Erro", description: "Função de envio MQTT não disponível!", variant: "destructive" });
      return;
    }

    console.log('📤 DevicePanel sending configuration:', {
      distanceThreshold_cm: distanceThreshold,
      globalTimeout_ms: globalTimeout,
      lastResortTimeout_ms: lastResortTimeout
    });

    activeDevices.forEach(device => {
      const config = {
        command: "configure",
        parameters: {
          distancethreshold: distanceThreshold, // valor em cm (não converter)
          globaltimeout: globalTimeout,
          lastresorttimeout: lastResortTimeout
        }
      };

      console.log(`📡 DevicePanel sending config to ${device.mac}:`, config);

      // Enviar via MQTT
      const topic = `/Module/Command/${device.mac}`;
      const payload = JSON.stringify(config);

      // Log do comando para debug
      if (window.mqttCommandLogger) {
        window.mqttCommandLogger(topic, config, 'sent');
      }

      publishMessage(topic, payload);
    });

    toast({
      title: "✅ Configuração de Dispositivos Enviada!",
      description: `${activeDevices.length} sensores: alcance ${distanceThreshold}cm, timeout ${globalTimeout}ms`
    });

    // Fechar o modal após enviar
    setIsConfigModalOpen(false);
  };

  const getStatusIndicator = (status) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="text-primary w-5 h-5" />;
      case 'offline':
        return <XCircle className="text-muted-foreground w-5 h-5" />;
      default:
        return <AlertTriangle className="text-yellow-500 w-5 h-5" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'online': return 'Online';
      case 'offline': return 'Offline';
      default: return 'Desconhecido';
    }
  }

  const handleAddDevice = () => {
    const macValidation = validateMacAddress(newDeviceMac);

    if (!macValidation.isValid) {
      toast({ title: "Erro", description: macValidation.error, variant: "destructive" });
      return;
    }

    addDevice({ mac: macValidation.value, name: '' });
    setNewDeviceMac('');
    setIsAddModalOpen(false);
    toast({ title: "Sucesso", description: "Dispositivo adicionado com sucesso!" });
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card className="bg-secondary border-border h-full flex flex-col">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="text-primary" />
                  Dispositivos
                </CardTitle>
                <CardDescription className="text-muted-foreground pt-1">
                  Gestão e status dos módulos de medição.
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-3 flex-grow">
            {devices.length === 0 ? (
              <p className="text-muted-foreground text-center py-4">Nenhum dispositivo adicionado.</p>
            ) : (
              <div className="max-h-[14rem] overflow-y-auto pr-2">
                {devices.map((device) => (
                  <div key={device.mac} className="p-3 mb-2 last:mb-0 rounded-lg bg-background flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      {getStatusIndicator(device.status)}
                      <div>
                        <div className="flex items-center gap-2">
                           <p className="font-bold text-sm text-foreground">{device.name || device.mac}</p>
                           <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => setEditingDevice(device)}>
                             <Edit className="h-3 w-3 text-muted-foreground" />
                           </Button>
                        </div>
                        {device.name && <p className="text-xs font-mono text-muted-foreground/70">{device.mac}</p>}
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                       <span className={`text-xs font-semibold ${device.active ? 'text-primary' : 'text-muted-foreground'}`}>
                         {device.active ? 'Ativo' : 'Inativo'}
                       </span>
                       <Switch
                        id={`switch-${device.mac}`}
                        checked={device.active}
                        onCheckedChange={() => toggleDeviceActive(device.mac)}
                        disabled={!isConnected}
                        aria-label={`Ativar/Desativar dispositivo ${device.mac}`}
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-end gap-2">
             <Button size="sm" onClick={() => setIsAddModalOpen(true)} disabled={!isConnected} variant="outline">
              <Plus className="w-4 h-4 mr-2" /> Adicionar
            </Button>
            <Button
              size="sm"
              onClick={() => setIsConfigModalOpen(true)}
              variant="outline"
            >
              <Settings className="w-4 h-4 mr-2" /> Configurações
            </Button>
          </CardFooter>
        </Card>
      </motion.div>



      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Novo Dispositivo</DialogTitle>
            <DialogDescription>
              Insira o endereço MAC do novo dispositivo para registá-lo no sistema.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="mac" className="text-right">
                MAC Address
              </Label>
              <Input
                id="mac"
                value={newDeviceMac}
                onChange={(e) => setNewDeviceMac(e.target.value.toUpperCase())}
                className="col-span-3 font-mono"
                placeholder="XX:XX:XX:XX:XX:XX"
                onKeyDown={(e) => e.key === 'Enter' && handleAddDevice()}
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleAddDevice}>Adicionar Dispositivo</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <EditDeviceModal
        isOpen={!!editingDevice}
        onOpenChange={() => setEditingDevice(null)}
        device={editingDevice}
        onSave={updateDevice}
      />

      {/* Modal de Configurações de Dispositivos */}
      <Dialog open={isConfigModalOpen} onOpenChange={setIsConfigModalOpen}>
        <DialogContent className="max-w-lg max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>⚙️ Configurações de Dispositivos</DialogTitle>
            <DialogDescription>
              Configurações enviadas via MQTT para todos os sensores ativos
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="modal-distanceThreshold">Alcance do Dispositivo (cm)</Label>
              <Input
                id="modal-distanceThreshold"
                type="number"
                min="30"
                max="200"
                value={distanceThreshold}
                onChange={(e) => setDistanceThreshold(parseInt(e.target.value) || 150)}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Distância máxima de detecção do sensor (30-200cm)
              </p>
            </div>

            <div>
              <Label htmlFor="modal-globalTimeout">Timeout de Reação (ms)</Label>
              <Input
                id="modal-globalTimeout"
                type="number"
                min="500"
                max="10000"
                value={globalTimeout}
                onChange={(e) => {
                  const newValue = parseInt(e.target.value) || 5000;
                  setGlobalTimeout(newValue);
                  // Ajustar lastResortTimeout se necessário
                  if (lastResortTimeout >= newValue) {
                    setLastResortTimeout(Math.max(1500, newValue - 500));
                  }
                }}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Tempo limite para passagem pelo sensor (500-10000ms)
              </p>
            </div>

            <div>
              <Label htmlFor="modal-lastResortTimeout">Tempo de Piscar (ms)</Label>
              <Input
                id="modal-lastResortTimeout"
                type="number"
                min="1500"
                max="5000"
                value={lastResortTimeout}
                onChange={(e) => {
                  const newValue = parseInt(e.target.value) || 3000;
                  // Garantir que lastResortTimeout seja sempre menor que globalTimeout
                  if (newValue >= globalTimeout) {
                    setLastResortTimeout(Math.max(1500, globalTimeout - 500));
                  } else {
                    setLastResortTimeout(newValue);
                  }
                }}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Tempo que o LED pisca antes do timeout (1500-5000ms)
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                💡 LED aceso: 0-{globalTimeout}ms | LED piscando: {globalTimeout}-{globalTimeout + lastResortTimeout}ms
              </p>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
              <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-2">📡 Comando MQTT</h5>
              <div className="text-xs text-blue-800 dark:text-blue-200 font-mono mb-2 break-all overflow-hidden">
                <code className="block p-2 bg-blue-100 dark:bg-blue-800 rounded text-wrap break-all">
                  {`{"command":"configure","parameters":{"distancethreshold":${distanceThreshold},"globaltimeout":${globalTimeout},"lastresorttimeout":${lastResortTimeout}}}`}
                </code>
              </div>
              {lastResortTimeout >= globalTimeout && (
                <div className="text-xs text-red-700 dark:text-red-300 mt-2 p-2 bg-red-100 dark:bg-red-900/20 rounded">
                  ⚠️ <strong>Configuração inválida:</strong> Tempo de piscar deve ser menor que timeout de reação!
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button
              onClick={handleConfigureDevices}
              disabled={!isConnected}
              className="w-full"
            >
              <Settings className="w-4 h-4 mr-2" />
              Enviar Configuração e Fechar
            </Button>
            {!isConnected && (
              <p className="text-xs text-muted-foreground text-center mt-2">
                ⚠️ Conecte ao MQTT para enviar configurações
              </p>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DevicePanel;