import React, { useMemo, useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { History, PlusCircle, BarChart2, FileDown, Edit } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const ParticipantEditModal = ({ isOpen, onOpenChange, participant, onSave, allNames }) => {
  const [name, setName] = useState('');
  const [age, setAge] = useState('');
  const [photo, setPhoto] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    if (participant) {
      setName(participant.name || '');
      setAge(participant.details?.age || '');
      setPhoto(participant.details?.photo || '');
    }
  }, [participant]);

  const handleSave = () => {
    if (!name.trim()) {
      toast({ title: 'Nome é obrigatório', variant: 'destructive' });
      return;
    }

    if (name !== participant?.name && allNames.includes(name)) {
      toast({ title: 'Nome já existe', variant: 'destructive' });
      return;
    }

    onSave(participant?.name, {
      name: name.trim(),
      details: { age: age.trim(), photo: photo.trim() }
    });
    onOpenChange(false);
    toast({ title: 'Participante atualizado com sucesso!' });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Editar Participante</DialogTitle>
          <DialogDescription>Atualize as informações do participante</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">Nome</Label>
            <Input id="name" value={name} onChange={(e) => setName(e.target.value)} />
          </div>
          <div>
            <Label htmlFor="age">Idade</Label>
            <Input id="age" value={age} onChange={(e) => setAge(e.target.value)} />
          </div>
          <div>
            <Label htmlFor="photo">URL da Foto</Label>
            <Input id="photo" value={photo} onChange={(e) => setPhoto(e.target.value)} />
          </div>
        </div>
        <DialogFooter>
          <Button onClick={handleSave}>Salvar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const SpeedHistoryTable = ({ participants, setParticipantName, onSelectRun, selectedRun, updateParticipant }) => {
  const [editingParticipant, setEditingParticipant] = useState(null);
  const { toast } = useToast();

  const handleExport = (name, runs) => {
    if (!runs || runs.length === 0) {
      toast({ title: 'Nenhum dado para exportar.', variant: 'destructive' });
      return;
    }

    const csvContent = [
      ['Data', 'Participante', 'Distância (m)', 'Tempo (s)', 'Velocidade (km/h)', 'Sensor 1', 'Sensor 2'].join(','),
      ...runs.map(run => [
        new Date(run.date).toISOString(),
        run.participantName || name,
        run.distance,
        run.totalTime.toFixed(3),
        run.speedKmh.toFixed(2),
        run.sensor1Time?.toFixed(3) || '',
        run.sensor2Time?.toFixed(3) || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${name}_velocidade.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({ title: `Dados de velocidade de ${name} exportados com sucesso!` });
  };

  const handleNewRun = (participantName) => {
    setParticipantName(participantName);
    toast({ title: `Participante ${participantName} selecionado para nova corrida.` });
  };

  const handleEditParticipant = (participant) => {
    setEditingParticipant(participant);
  };

  const handleExportAll = () => {
    const allRuns = Object.values(participants).flatMap(p => p.runs || []);
    
    if (allRuns.length === 0) {
      toast({ title: 'Nenhum dado de velocidade para exportar.', variant: 'destructive' });
      return;
    }
    
    handleExport('todos_participantes_velocidade', allRuns);
  };

  const sortedParticipants = useMemo(() => {
    return Object.entries(participants)
      .map(([name, data]) => {
        const { runs, ...details } = data;
        
        // Processar apenas runs de velocidade
        let bestRun = null;
        let latestRunDate = null;
        if (runs && runs.length > 0) {
          bestRun = runs.reduce((best, current) => (current.speedKmh > (best.speedKmh || 0) ? current : best), runs[0]);
          latestRunDate = new Date(Math.max(...runs.map(run => new Date(run.date))));
        }
        
        return { 
          name, 
          runs: runs || [], 
          details, 
          bestRun, 
          latestDate: latestRunDate 
        };
      })
      .filter(({ runs }) => runs.length > 0) // Mostrar apenas participantes com dados de velocidade
      .sort((a, b) => {
        if (!b.latestDate) return -1;
        if (!a.latestDate) return 1;
        return b.latestDate - a.latestDate;
      });
  }, [participants]);

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-6 w-6 text-blue-500" />
                  🏃‍♂️ Histórico de Velocidade
                </CardTitle>
                <CardDescription>
                  Histórico de corridas e medições de velocidade dos participantes
                </CardDescription>
              </div>
              <Button onClick={handleExportAll} variant="outline" size="sm">
                <FileDown className="h-4 w-4 mr-2" />
                Exportar Todos
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {sortedParticipants.length === 0 ? (
              <p className="text-muted-foreground text-center py-8">Nenhuma corrida de velocidade registrada.</p>
            ) : (
              <Accordion type="single" collapsible className="w-full">
                {sortedParticipants.map(({ name, runs, details, bestRun }) => (
                  <AccordionItem value={name} key={name}>
                    <AccordionTrigger className="hover:bg-white/5 px-4 rounded-md">
                      <div className="flex justify-between items-center w-full">
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage src={details.photo} alt={name} />
                            <AvatarFallback>{name.substring(0, 2).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <div>
                            <span className="font-bold text-lg text-foreground">{name}</span>
                            {details.age && <p className="text-sm text-muted-foreground">{details.age} anos</p>}
                          </div>
                        </div>

                        <div className="flex items-center gap-2 text-sm">
                          {bestRun ? (
                            <span className="hidden sm:inline text-muted-foreground">Melhor: <span className="font-semibold text-primary">{bestRun.speedKmh.toFixed(2)} km/h</span></span>
                          ) : (
                            <span className="hidden sm:inline text-muted-foreground">Sem corridas</span>
                          )}
                          <span className="hidden md:inline text-muted-foreground">
                            Corridas: <span className="font-semibold text-primary">{runs.length}</span>
                          </span>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Data</TableHead>
                              <TableHead className="text-center">Distância (m)</TableHead>
                              <TableHead className="text-center">Tempo (s)</TableHead>
                              <TableHead className="text-center">Velocidade (km/h)</TableHead>
                              <TableHead className="text-right">Ações</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {runs.sort((a, b) => new Date(b.date) - new Date(a.date)).map((run) => (
                              <TableRow key={run.id} className="border-b-0">
                                <TableCell>{new Date(run.date).toLocaleString('pt-BR')}</TableCell>
                                <TableCell className="text-center">{run.distance}</TableCell>
                                <TableCell className="text-center">{run.totalTime.toFixed(3)}</TableCell>
                                <TableCell className="text-center font-bold text-primary">{run.speedKmh.toFixed(2)}</TableCell>
                                <TableCell className="text-right">
                                  <Button variant="outline" size="sm" onClick={() => onSelectRun(run)}>
                                    <BarChart2 className="h-4 w-4 mr-2" />
                                    Ver Detalhes
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </CardContent>
        </Card>
      </motion.div>
      
      <ParticipantEditModal
        isOpen={!!editingParticipant}
        onOpenChange={() => setEditingParticipant(null)}
        participant={editingParticipant}
        onSave={updateParticipant}
        allNames={Object.keys(participants)}
      />
    </>
  );
};

export default SpeedHistoryTable;
