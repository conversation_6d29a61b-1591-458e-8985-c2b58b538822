import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Trash2, Send, Eye } from 'lucide-react';

const MqttCommandLog = ({ isVisible = false }) => {
  const [commands, setCommands] = useState([]);

  // Interceptar comandos MQTT (simulação para demonstração)
  useEffect(() => {
    // Esta é uma implementação de exemplo
    // Na aplicação real, você conectaria isso ao sistema MQTT
    const addCommand = (topic, payload, type = 'sent') => {
      const newCommand = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        topic,
        payload: JSON.stringify(payload, null, 2),
        type, // 'sent' ou 'received'
        status: 'success'
      };
      
      setCommands(prev => [newCommand, ...prev.slice(0, 49)]); // <PERSON><PERSON> apenas 50 comandos
    };

    // Exemplo de como seria usado
    window.mqttCommandLogger = addCommand;

    return () => {
      delete window.mqttCommandLogger;
    };
  }, []);

  const clearCommands = () => {
    setCommands([]);
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'sent': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'received': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'error': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'sent': return <Send className="w-3 h-3" />;
      case 'received': return <Eye className="w-3 h-3" />;
      default: return null;
    }
  };

  if (!isVisible) return null;

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Send className="w-5 h-5" />
              Log de Comandos MQTT
            </CardTitle>
            <CardDescription>
              Comandos enviados e recebidos em tempo real
            </CardDescription>
          </div>
          <Button onClick={clearCommands} size="sm" variant="outline">
            <Trash2 className="w-4 h-4 mr-2" />
            Limpar
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {commands.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Send className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Nenhum comando registado ainda.</p>
            <p className="text-sm mt-2">
              Os comandos MQTT aparecerão aqui quando enviados.
            </p>
          </div>
        ) : (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {commands.map((command) => (
              <div key={command.id} className="border rounded-lg p-3 space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge className={getTypeColor(command.type)}>
                      {getTypeIcon(command.type)}
                      <span className="ml-1 capitalize">{command.type}</span>
                    </Badge>
                    <span className="text-sm font-mono text-muted-foreground">
                      {new Date(command.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
                
                <div className="space-y-1">
                  <div className="text-sm">
                    <span className="font-medium">Tópico:</span>
                    <code className="ml-2 bg-muted px-1 rounded text-xs">
                      {command.topic}
                    </code>
                  </div>
                  
                  <div className="text-sm">
                    <span className="font-medium">Payload:</span>
                    <pre className="mt-1 bg-muted p-2 rounded text-xs overflow-x-auto">
                      {command.payload}
                    </pre>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Exemplos de comandos */}
        <div className="mt-6 pt-4 border-t">
          <h4 className="font-medium mb-3">📋 Comandos do Modo Reação:</h4>
          <div className="space-y-2 text-sm">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
              <strong>Configuração:</strong>
              <pre className="text-xs mt-1 font-mono">
{`Tópico: /Module/Command/[MAC]
Payload: {
  "command": "configure",
  "parameters": {
    "distancethreshold": 150,
    "globaltimeout": 5000,
    "lastresorttimeout": 3000
  }
}`}
              </pre>
            </div>
            
            <div className="bg-green-50 dark:bg-green-900/20 p-2 rounded">
              <strong>Ativação LED:</strong>
              <pre className="text-xs mt-1 font-mono">
{`Tópico: /Module/Command/[MAC]
Payload: {
  "command": "timedactivate"
}`}
              </pre>
            </div>
            
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-2 rounded">
              <strong>Resposta Esperada:</strong>
              <pre className="text-xs mt-1 font-mono">
{`Tópico: /Module/Telemetry/[MAC]
Payload: {
  "pass": "true",
  "timestamp": 1234567890123
}`}
              </pre>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MqttCommandLog;
