import React, { useMemo, useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { History, PlusCircle, FileDown, Edit, Eye, Download } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const ParticipantEditModal = ({ isOpen, onOpenChange, participant, onSave, allNames }) => {
  const [name, setName] = useState('');
  const [age, setAge] = useState('');
  const [photo, setPhoto] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    if (participant) {
      setName(participant.name || '');
      setAge(participant.details?.age || '');
      setPhoto(participant.details?.photo || '');
    }
  }, [participant]);

  const handleSave = () => {
    if (!name.trim()) {
      toast({ title: 'Nome é obrigatório', variant: 'destructive' });
      return;
    }

    if (name !== participant?.name && allNames.includes(name)) {
      toast({ title: 'Nome já existe', variant: 'destructive' });
      return;
    }

    onSave(participant?.name, {
      name: name.trim(),
      details: { age: age.trim(), photo: photo.trim() }
    });
    onOpenChange(false);
    toast({ title: 'Participante atualizado com sucesso!' });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Editar Participante</DialogTitle>
          <DialogDescription>Atualize as informações do participante</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">Nome</Label>
            <Input id="name" value={name} onChange={(e) => setName(e.target.value)} />
          </div>
          <div>
            <Label htmlFor="age">Idade</Label>
            <Input id="age" value={age} onChange={(e) => setAge(e.target.value)} />
          </div>
          <div>
            <Label htmlFor="photo">URL da Foto</Label>
            <Input id="photo" value={photo} onChange={(e) => setPhoto(e.target.value)} />
          </div>
        </div>
        <DialogFooter>
          <Button onClick={handleSave}>Salvar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const ReactionHistoryTable = ({ participants, setParticipantName, updateParticipant }) => {
  const [editingParticipant, setEditingParticipant] = useState(null);
  const [expandedGames, setExpandedGames] = useState(new Set());
  const { toast } = useToast();

  const toggleGameDetails = (gameId) => {
    const newExpanded = new Set(expandedGames);
    if (newExpanded.has(gameId)) {
      newExpanded.delete(gameId);
    } else {
      newExpanded.add(gameId);
    }
    setExpandedGames(newExpanded);
  };

  const handleExport = (name, reactionGames) => {
    if (!reactionGames || reactionGames.length === 0) {
      toast({ title: 'Nenhum dado para exportar.', variant: 'destructive' });
      return;
    }

    const csvContent = [
      ['Data', 'Participante', 'Rondas', 'Sucessos', 'Falhas', 'Precisão (%)', 'Tempo Médio (ms)', 'Tempo de Jogo (s)'].join(','),
      ...reactionGames.map(game => [
        new Date(game.date).toISOString(),
        game.participantName || name,
        game.totalRounds,
        game.successes,
        game.misses,
        game.accuracy.toFixed(1),
        game.averageReactionTime.toFixed(0),
        game.gameTime?.toFixed(1) || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${name}_reacao.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({ title: `Dados de reação de ${name} exportados com sucesso!` });
  };

  const handleExportSingleGame = (game, participantName) => {
    if (!game || !game.reactions || game.reactions.length === 0) {
      toast({ title: 'Nenhum dado para exportar neste jogo.', variant: 'destructive' });
      return;
    }

    // Calcular estatísticas adicionais
    const successfulReactions = game.reactions.filter(r => r.success);
    const reactionTimes = successfulReactions.map(r => r.reactionTime);
    const speeds = game.reactions.map(r => r.speed).filter(s => s > 0);

    // Calcular mediana
    const sortedTimes = [...reactionTimes].sort((a, b) => a - b);
    const median = sortedTimes.length > 0
      ? sortedTimes.length % 2 === 0
        ? (sortedTimes[sortedTimes.length / 2 - 1] + sortedTimes[sortedTimes.length / 2]) / 2
        : sortedTimes[Math.floor(sortedTimes.length / 2)]
      : 0;

    // Calcular velocidade média
    const averageSpeed = speeds.length > 0 ? speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length : 0;

    // Calcular melhor e pior tempo
    const bestTime = reactionTimes.length > 0 ? Math.min(...reactionTimes) : 0;
    const worstTime = reactionTimes.length > 0 ? Math.max(...reactionTimes) : 0;

    // Seção 1: Análise do Jogo
    const analysisSection = [
      '=== ANÁLISE DO JOGO ===',
      'Métrica,Valor',
      `Participante,${participantName}`,
      `Data do Jogo,${new Date(game.date).toLocaleString('pt-BR')}`,
      `Total de Rondas,${game.totalRounds}`,
      `Sucessos,${game.successes}`,
      `Falhas,${game.misses}`,
      `Precisão (%),${game.accuracy.toFixed(1)}`,
      `Tempo Médio de Reação (ms),${game.averageReactionTime.toFixed(0)}`,
      `Melhor Tempo (ms),${bestTime.toFixed(0)}`,
      `Pior Tempo (ms),${worstTime.toFixed(0)}`,
      `Tempo Total de Jogo (s),${game.gameTime ? game.gameTime.toFixed(1) : 'N/A'}`,
      '',
      '=== ANÁLISE DE TEMPO ===',
      `Tempo mais rápido:,${bestTime.toFixed(0)}ms`,
      `Tempo mais lento:,${worstTime.toFixed(0)}ms`,
      `Mediana:,${median.toFixed(0)}ms`,
      '',
      '=== ANÁLISE DE VELOCIDADE ===',
      `Velocidade máxima:,${game.maxSpeed.toFixed(1)} km/h`,
      `Velocidade média:,${averageSpeed.toFixed(1)} km/h`,
      `Reações com velocidade:,${speeds.length}`,
      '',
      ''
    ];

    // Seção 2: Detalhes das Reações
    const detailsSection = [
      '=== DETALHES DAS REAÇÕES ===',
      'Ronda,Dispositivo,Tempo_Reacao_ms,Sucesso,Velocidade_kmh,Data_Hora',
      ...game.reactions.map(reaction => [
        reaction.round,
        reaction.device.name || reaction.device.mac,
        reaction.reactionTime,
        reaction.success ? 'Sim' : 'Não',
        reaction.speed.toFixed(2),
        reaction.timestamp ? new Date(reaction.timestamp).toLocaleString('pt-BR') : 'N/A'
      ].join(','))
    ];

    const csvContent = [
      ...analysisSection,
      ...detailsSection
    ].join('\n');

    const gameDate = new Date(game.date).toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `reaction-game-${participantName}-${gameDate}.csv`;

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({ title: `Jogo de ${participantName} exportado com sucesso!` });
  };

  const handleNewGame = (participantName) => {
    setParticipantName(participantName);
    toast({ title: `Participante ${participantName} selecionado para novo jogo de reação.` });
  };

  const handleEditParticipant = (participant) => {
    setEditingParticipant(participant);
  };

  const handleExportAll = () => {
    const allReactionGames = Object.values(participants).flatMap(p => p.reactionGames || []);
    
    if (allReactionGames.length === 0) {
      toast({ title: 'Nenhum dado de reação para exportar.', variant: 'destructive' });
      return;
    }
    
    handleExport('todos_participantes_reacao', allReactionGames);
  };

  const sortedParticipants = useMemo(() => {
    return Object.entries(participants)
      .map(([name, data]) => {
        const { reactionGames, ...details } = data;
        
        // Processar apenas jogos de reação
        let bestReactionGame = null;
        let latestReactionDate = null;
        if (reactionGames && reactionGames.length > 0) {
          bestReactionGame = reactionGames.reduce((best, current) => (current.accuracy > (best.accuracy || 0) ? current : best), reactionGames[0]);
          latestReactionDate = new Date(Math.max(...reactionGames.map(game => new Date(game.date))));
        }
        
        return { 
          name, 
          reactionGames: reactionGames || [], 
          details, 
          bestReactionGame,
          latestDate: latestReactionDate 
        };
      })
      .filter(({ reactionGames }) => reactionGames.length > 0) // Mostrar apenas participantes com dados de reação
      .sort((a, b) => {
        if (!b.latestDate) return -1;
        if (!a.latestDate) return 1;
        return b.latestDate - a.latestDate;
      });
  }, [participants]);

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <span className="material-symbols-rounded text-primary" style={{ fontSize: '24px' }}>history</span>
                  Histórico de Reação
                </CardTitle>
                <CardDescription>
                  Histórico de jogos de reação e estatísticas dos participantes
                </CardDescription>
              </div>
              <Button onClick={handleExportAll} variant="outline" size="sm">
                <FileDown className="h-4 w-4 mr-2" />
                Exportar Todos
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {sortedParticipants.length === 0 ? (
              <p className="text-muted-foreground text-center py-8">Nenhum jogo de reação registrado.</p>
            ) : (
              <Accordion type="single" collapsible className="w-full">
                {sortedParticipants.map(({ name, reactionGames, details, bestReactionGame }) => (
                  <AccordionItem value={name} key={name}>
                    <AccordionTrigger className="hover:bg-white/5 px-4 rounded-md">
                      <div className="flex justify-between items-center w-full">
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage src={details.photo} alt={name} />
                            <AvatarFallback>{name.substring(0, 2).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <div>
                            <span className="font-bold text-lg text-foreground">{name}</span>
                            {details.age && <p className="text-sm text-muted-foreground">{details.age} anos</p>}
                          </div>
                        </div>

                        <div className="flex items-center gap-2 text-sm">
                          {bestReactionGame ? (
                            <span className="hidden sm:inline text-muted-foreground">Melhor: <span className="font-semibold text-primary">{bestReactionGame.accuracy.toFixed(1)}%</span></span>
                          ) : (
                            <span className="hidden sm:inline text-muted-foreground">Sem jogos</span>
                          )}
                          <span className="hidden md:inline text-muted-foreground">
                            Jogos: <span className="font-semibold text-primary">{reactionGames.length}</span>
                          </span>
                          <Button size="icon" variant="ghost" onClick={(e) => { e.stopPropagation(); handleEditParticipant({name, details}); }}>
                            <Edit className="h-5 w-5 text-yellow-400" />
                          </Button>
                          <Button size="icon" variant="ghost" onClick={(e) => { 
                            e.stopPropagation(); 
                            handleExport(name, reactionGames);
                          }} disabled={reactionGames.length === 0}>
                            <FileDown className="h-5 w-5 text-blue-400" />
                          </Button>
                          <Button size="icon" variant="ghost" onClick={(e) => { e.stopPropagation(); handleNewGame(name); }}>
                            <PlusCircle className="h-5 w-5 text-primary" />
                          </Button>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Data</TableHead>
                              <TableHead className="text-center">Rondas</TableHead>
                              <TableHead className="text-center">Sucessos</TableHead>
                              <TableHead className="text-center">Falhas</TableHead>
                              <TableHead className="text-center">Precisão (%)</TableHead>
                              <TableHead className="text-center">Tempo Médio (ms)</TableHead>
                              <TableHead className="text-center">Ações</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {reactionGames.sort((a, b) => new Date(b.date) - new Date(a.date)).map((game) => (
                              <React.Fragment key={game.id}>
                                <TableRow className="border-b-0">
                                  <TableCell>{new Date(game.date).toLocaleString('pt-BR')}</TableCell>
                                  <TableCell className="text-center">{game.totalRounds}</TableCell>
                                  <TableCell className="text-center text-primary font-semibold">{game.successes}</TableCell>
                                  <TableCell className="text-center text-muted-foreground font-semibold">{game.misses}</TableCell>
                                  <TableCell className="text-center font-bold text-primary">{game.accuracy.toFixed(1)}%</TableCell>
                                  <TableCell className="text-center">{game.averageReactionTime.toFixed(0)}</TableCell>
                                  <TableCell className="text-center">
                                    <div className="flex gap-1 justify-center">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => toggleGameDetails(game.id)}
                                        className="h-8 w-8 p-0"
                                      >
                                        <Eye className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleExportSingleGame(game, name)}
                                        className="h-8 w-8 p-0"
                                        disabled={!game.reactions || game.reactions.length === 0}
                                      >
                                        <Download className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </TableCell>
                                </TableRow>
                                {expandedGames.has(game.id) && game.reactions && game.reactions.length > 0 && (
                                  <TableRow>
                                    <TableCell colSpan={7} className="p-0">
                                      <div
                                        className="p-4 border-t"
                                        style={{
                                          backgroundColor: 'hsl(240, 4%, 26%)',
                                          borderTopColor: 'hsl(240, 4%, 36%)'
                                        }}
                                      >
                                        <h4 className="font-semibold mb-3 text-sm" style={{ color: 'hsl(240, 4%, 90%)' }}>
                                          Detalhes das Reações:
                                        </h4>
                                        <div className="overflow-x-auto">
                                          <Table>
                                            <TableHeader>
                                              <TableRow style={{ borderBottomColor: 'hsl(240, 4%, 36%)' }}>
                                                <TableHead className="text-xs" style={{ color: 'hsl(240, 4%, 70%)' }}>Ronda</TableHead>
                                                <TableHead className="text-xs" style={{ color: 'hsl(240, 4%, 70%)' }}>Dispositivo</TableHead>
                                                <TableHead className="text-xs" style={{ color: 'hsl(240, 4%, 70%)' }}>Tempo (ms)</TableHead>
                                                <TableHead className="text-xs" style={{ color: 'hsl(240, 4%, 70%)' }}>Sucesso</TableHead>
                                                <TableHead className="text-xs" style={{ color: 'hsl(240, 4%, 70%)' }}>Velocidade (km/h)</TableHead>
                                                <TableHead className="text-xs" style={{ color: 'hsl(240, 4%, 70%)' }}>Data/Hora</TableHead>
                                              </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                              {game.reactions.map((reaction, index) => (
                                                <TableRow
                                                  key={index}
                                                  className="text-xs hover:opacity-80 transition-opacity"
                                                  style={{ borderBottomColor: 'hsl(240, 4%, 36%)' }}
                                                >
                                                  <TableCell className="text-center" style={{ color: 'hsl(240, 4%, 85%)' }}>
                                                    {reaction.round}
                                                  </TableCell>
                                                  <TableCell style={{ color: 'hsl(240, 4%, 85%)' }}>
                                                    {reaction.device.name || reaction.device.mac}
                                                  </TableCell>
                                                  <TableCell className="text-center">
                                                    <span className={reaction.success ? 'font-medium' : 'font-medium'}
                                                          style={{ color: reaction.success ? 'hsl(38, 92%, 50%)' : 'hsl(240, 5%, 64.9%)' }}>
                                                      {reaction.reactionTime}
                                                    </span>
                                                  </TableCell>
                                                  <TableCell className="text-center">
                                                    <span className={reaction.success ? 'font-semibold' : 'font-semibold'}
                                                          style={{ color: reaction.success ? 'hsl(38, 92%, 50%)' : 'hsl(240, 5%, 64.9%)' }}>
                                                      {reaction.success ? 'Sim' : 'Não'}
                                                    </span>
                                                  </TableCell>
                                                  <TableCell className="text-center" style={{ color: 'hsl(240, 4%, 85%)' }}>
                                                    {reaction.speed.toFixed(2)}
                                                  </TableCell>
                                                  <TableCell className="text-xs" style={{ color: 'hsl(240, 4%, 70%)' }}>
                                                    {reaction.timestamp ? new Date(reaction.timestamp).toLocaleString('pt-BR') : 'N/A'}
                                                  </TableCell>
                                                </TableRow>
                                              ))}
                                            </TableBody>
                                          </Table>
                                        </div>
                                      </div>
                                    </TableCell>
                                  </TableRow>
                                )}
                              </React.Fragment>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </CardContent>
        </Card>
      </motion.div>
      
      <ParticipantEditModal
        isOpen={!!editingParticipant}
        onOpenChange={() => setEditingParticipant(null)}
        participant={editingParticipant}
        onSave={updateParticipant}
        allNames={Object.keys(participants)}
      />
    </>
  );
};

export default ReactionHistoryTable;
