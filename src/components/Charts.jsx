import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, User, FileDown } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { exportToCSV } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';

const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const runData = payload[0].payload;
    return (
      <div
        className="p-3 border shadow-lg rounded-md"
        style={{
          backgroundColor: 'hsl(240, 4%, 26%)',
          borderColor: 'hsl(240, 4%, 36%)',
          '--tw-shadow-color': 'hsl(240, 4%, 26%)',
          '--tw-shadow': 'var(--tw-shadow-colored)'
        }}
      >
        <p className="font-semibold" style={{ color: 'hsl(240, 4%, 90%)' }}>{`${runData.name}`}</p>
        <p className="text-orange-400">{`Velocidade: ${runData.velocidade.toFixed(2)} km/h`}</p>
        <p style={{ color: 'hsl(240, 4%, 85%)' }}>{`Tempo: ${runData.tempo.toFixed(2)} s`}</p>
        <p className="text-xs" style={{ color: 'hsl(240, 4%, 70%)' }}>{runData.date}</p>
      </div>
    );
  }
  return null;
};

const Charts = ({ participants }) => {
  const [selectedParticipant, setSelectedParticipant] = useState('all');
  const { toast } = useToast();

  const chartData = useMemo(() => {
    const participantKeys = Object.keys(participants);
    if(participantKeys.length === 0) return [];

    if (selectedParticipant === 'all') {
      const allRuns = participantKeys.flatMap(name => 
        participants[name].runs.map(run => ({ ...run, name }))
      ).sort((a, b) => new Date(a.date) - new Date(b.date));
      
      return allRuns.slice(-10).map(m => ({
        name: m.name.slice(0, 10) + (m.name.length > 10 ? '...' : ''),
        velocidade: m.speedKmh,
        tempo: m.totalTime,
        date: new Date(m.date).toLocaleDateString('pt-BR'),
      }));
    } else {
      const participantRuns = participants[selectedParticipant]?.runs || [];
      return participantRuns
        .sort((a, b) => b.speedKmh - a.speedKmh) // Sort by best speed
        .slice(0, 10) // Take top 10
        .map((m, index) => ({
          name: `#${index + 1}`,
          velocidade: m.speedKmh,
          tempo: m.totalTime,
          date: new Date(m.date).toLocaleDateString('pt-BR'),
        }));
    }
  }, [participants, selectedParticipant]);

  const handleExport = () => {
    if(chartData.length === 0) {
      toast({
        title: 'Nenhum dado para exportar.',
        variant: 'destructive',
      });
      return;
    }
    const filename = `desempenho_${selectedParticipant === 'all' ? 'geral' : selectedParticipant.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`;
    exportToCSV(chartData, filename);
    toast({
      title: 'Exportação Concluída!',
      description: `O ficheiro ${filename} foi descarregado.`,
    });
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card className="bg-secondary border-border">
          <CardHeader>
            <div className="flex justify-between items-start flex-wrap gap-4">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="text-primary" />
                  Análise de Desempenho
                </CardTitle>
                <CardDescription>
                  {selectedParticipant === 'all' 
                    ? 'Últimas 10 medições de todos os participantes' 
                    : `Top 10 melhores medições de ${selectedParticipant}`}
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Select value={selectedParticipant} onValueChange={setSelectedParticipant} disabled={Object.keys(participants).length === 0}>
                  <SelectTrigger className="w-[220px]">
                    <User className="h-4 w-4 mr-2 text-muted-foreground" />
                    <SelectValue placeholder="Selecione um participante" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os Participantes</SelectItem>
                    {Object.keys(participants).sort().map(name => (
                      <SelectItem key={name} value={name}>{name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button onClick={handleExport} variant="outline" size="icon" disabled={chartData.length === 0}>
                  <FileDown className="h-4 w-4" />
                  <span className="sr-only">Exportar para CSV</span>
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className={Object.keys(participants).length === 0 ? "text-center py-10" : "grid grid-cols-1 lg:grid-cols-2 gap-8"}>
            {Object.keys(participants).length === 0 ? (
                <p className="text-muted-foreground">Nenhuma medição registrada para exibir gráficos.</p>
            ) : (
              <>
                <div>
                  <h4 className="text-center text-muted-foreground mb-2">Velocidades (km/h)</h4>
                  <ResponsiveContainer width="100%" height={250}>
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                      <XAxis dataKey="name" stroke="hsl(var(--muted-foreground))" fontSize={12} tickLine={false} axisLine={false} />
                      <YAxis stroke="hsl(var(--muted-foreground))" fontSize={12} tickLine={false} axisLine={false} />
                      <Tooltip
                        content={<CustomTooltip />}
                        cursor={{ stroke: 'hsl(240, 4%, 26%)', strokeWidth: 2 }}
                      />
                      <Line type="monotone" dataKey="velocidade" stroke="hsl(var(--primary))" strokeWidth={2} dot={{ r: 4, fill: 'hsl(var(--primary))' }} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
                <div>
                  <h4 className="text-center text-muted-foreground mb-2">Tempos (s)</h4>
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                      <XAxis dataKey="name" stroke="hsl(var(--muted-foreground))" fontSize={12} tickLine={false} axisLine={false} />
                      <YAxis stroke="hsl(var(--muted-foreground))" fontSize={12} tickLine={false} axisLine={false} />
                      <Tooltip
                        content={<CustomTooltip />}
                        cursor={{ fill: 'hsl(240, 4%, 26%)', opacity: 0.3 }}
                      />
                      <Bar dataKey="tempo" fill="hsl(var(--primary))" opacity={0.5} radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </>
  );
};

export default Charts;