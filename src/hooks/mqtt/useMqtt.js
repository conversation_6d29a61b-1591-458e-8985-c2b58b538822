import { useRef, useCallback, useEffect, useState } from 'react';
import mqtt from 'mqtt';
import { useToast } from '@/components/ui/use-toast';
import { MQTT_BROKER_URL, MQTT_CREDENTIALS, TELEMETRY_TOPIC_PREFIX } from './constants';

export const useMqtt = ({ devices, setDevices, onMessage, addLog }) => {
  const clientRef = useRef(null);
  const [mqttStatus, setMqttStatus] = useState('Disconnected');
  const { toast } = useToast();
  const devicesRef = useRef(devices);

  useEffect(() => {
    devicesRef.current = devices;
  }, [devices]);

  const connectMqtt = useCallback(() => {
    if (clientRef.current && (clientRef.current.connected || clientRef.current.reconnecting)) {
      return;
    }

    setMqttStatus('Connecting...');

    clientRef.current = mqtt.connect(MQTT_BROKER_URL, {
      ...MQTT_CREDENTIALS,
      clean: true,
      reconnectPeriod: 5000,
      connectTimeout: 10000,
    });

    clientRef.current.on('connect', () => {
      setMqttStatus('Connected');
      toast({ title: "Sucesso", description: "Ligado ao broker MQTT!" });

      const currentDevices = devicesRef.current;
      if (currentDevices.length > 0) {
        const topicsToSubscribe = currentDevices.map(d => `${TELEMETRY_TOPIC_PREFIX}${d.mac}`);
        console.log('📡 Subscribing to topics:', topicsToSubscribe);
        clientRef.current.subscribe(topicsToSubscribe, (err) => {
          if (err) {
            console.log('❌ Subscription error:', err);
            toast({ title: "Erro de Subscrição", description: `Não foi possível subscrever aos tópicos. ${err.message}`, variant: "destructive" });
            setMqttStatus('Error');
          } else {
             console.log('✅ Successfully subscribed to topics:', topicsToSubscribe);
             setDevices(prev => prev.map(d => ({ ...d, status: 'online' })));
             toast({ title: "Subscrição", description: `Subscrito a ${topicsToSubscribe.length} tópicos de telemetria.` });
          }
        });
      }
    });

    clientRef.current.on('message', onMessage);
    clientRef.current.on('error', (err) => {
      setMqttStatus('Error');
      toast({ title: "Erro MQTT", description: `Falha na ligação: ${err.message}. Verifique as credenciais.`, variant: "destructive" });
      clientRef.current.end(true);
    });
    clientRef.current.on('close', () => {
      if (mqttStatus !== 'Disconnected') {
        setMqttStatus('Disconnected');
        setDevices(prev => prev.map(d => ({ ...d, status: 'offline' })));
      }
    });
    clientRef.current.on('reconnect', () => {
        setMqttStatus('Connecting...');
        setDevices(prev => prev.map(d => ({ ...d, status: 'offline' })));
    });
  }, [onMessage, toast, setDevices, mqttStatus]);
  
  const disconnectMqtt = () => {
    if (clientRef.current) {
      setMqttStatus('Disconnected');
      clientRef.current.end(true);
      clientRef.current = null;
      setDevices(prev => prev.map(d => ({ ...d, status: 'offline' })));
      toast({ title: "Info", description: "Desligado do broker MQTT." });
    }
  };
  
  const subscribeToDevice = (mac) => {
    if (clientRef.current && clientRef.current.connected) {
      clientRef.current.subscribe(`${TELEMETRY_TOPIC_PREFIX}${mac}`, (err) => {
        if (!err) {
           setDevices(prev => prev.map(d => d.mac === mac ? { ...d, status: 'online' } : d));
        } else {
           toast({ title: "Erro de Subscrição", description: `Não foi possível subscrever ao novo dispositivo. ${err.message}`, variant: "destructive" });
        }
      });
    }
  };

  return { clientRef, mqttStatus, connectMqtt, disconnectMqtt, subscribeToDevice };
};