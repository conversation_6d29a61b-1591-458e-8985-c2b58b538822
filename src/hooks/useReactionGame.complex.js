import { useState, useEffect, useRef, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

export const useReactionGame = ({ devices, sendCommandToDevice, mqttStatus, participantName, addReactionGameToParticipant }) => {
  const { toast } = useToast();
  
  // Estado do jogo
  const [gameState, setGameState] = useState('idle'); // 'idle', 'running', 'paused', 'finished'
  const [currentRound, setCurrentRound] = useState(0);
  const [totalRounds, setTotalRounds] = useState(10);
  const [gameTime, setGameTime] = useState(0);
  const [maxGameTime, setMaxGameTime] = useState(60); // segundos
  
  // Configurações
  const [reactionTimeout, setReactionTimeout] = useState(3000); // ms para reagir
  const [nextActivationDelay, setNextActivationDelay] = useState(1500); // ms entre ativações
  const [distance, setDistance] = useState(1); // metros (para cálculo de velocidade)
  
  // Estado atual
  const [activeDevice, setActiveDevice] = useState(null);
  const [activationTime, setActivationTime] = useState(null);
  const [waitingForReaction, setWaitingForReaction] = useState(false);
  
  // Contadores
  const [successes, setSuccesses] = useState(0);
  const [misses, setMisses] = useState(0);
  const [reactions, setReactions] = useState([]); // Array de {device, activationTime, reactionTime, success, speed}
  
  // Refs para timeouts
  const reactionTimeoutRef = useRef(null);
  const nextActivationTimeoutRef = useRef(null);
  const gameTimerRef = useRef(null);
  
  // Refs para valores atuais
  const gameStateRef = useRef(gameState);
  const activeDeviceRef = useRef(activeDevice);
  const activationTimeRef = useRef(activationTime);
  const waitingForReactionRef = useRef(waitingForReaction);
  
  useEffect(() => { gameStateRef.current = gameState; }, [gameState]);
  useEffect(() => { activeDeviceRef.current = activeDevice; }, [activeDevice]);
  useEffect(() => { activationTimeRef.current = activationTime; }, [activationTime]);
  useEffect(() => { waitingForReactionRef.current = waitingForReaction; }, [waitingForReaction]);
  
  // Timer do jogo
  useEffect(() => {
    if (gameState === 'running') {
      gameTimerRef.current = setInterval(() => {
        setGameTime(prev => {
          const newTime = prev + 1;
          if (newTime >= maxGameTime) {
            endGame();
          }
          return newTime;
        });
      }, 1000);
    } else {
      if (gameTimerRef.current) {
        clearInterval(gameTimerRef.current);
        gameTimerRef.current = null;
      }
    }
    
    return () => {
      if (gameTimerRef.current) {
        clearInterval(gameTimerRef.current);
      }
    };
  }, [gameState, maxGameTime]);
  
  // Limpar timeouts ao desmontar
  useEffect(() => {
    return () => {
      if (reactionTimeoutRef.current) clearTimeout(reactionTimeoutRef.current);
      if (nextActivationTimeoutRef.current) clearTimeout(nextActivationTimeoutRef.current);
      if (gameTimerRef.current) clearInterval(gameTimerRef.current);
    };
  }, []);
  
  // Selecionar dispositivo aleatório
  const getRandomDevice = useCallback(() => {
    const activeDevices = devices.filter(d => d.active && d.status === 'online');
    if (activeDevices.length === 0) return null;
    return activeDevices[Math.floor(Math.random() * activeDevices.length)];
  }, [devices]);
  
  // Ativar próximo dispositivo
  const activateNextDevice = useCallback(() => {
    console.log('🎯 activateNextDevice called, gameState:', gameStateRef.current);

    if (gameStateRef.current !== 'running') {
      console.log('❌ Game not running, aborting activation');
      return;
    }

    const device = getRandomDevice();
    console.log('🎲 Random device selected:', device);

    if (!device) {
      console.log('❌ No device available');
      toast({ title: "Erro", description: "Nenhum dispositivo ativo disponível", variant: "destructive" });
      return;
    }

    const now = Date.now();
    console.log('⚡ Activating device:', { device: device.mac, timestamp: now });

    setActiveDevice(device);
    setActivationTime(now);
    setWaitingForReaction(true);

    // Atualizar refs imediatamente
    activeDeviceRef.current = device;
    activationTimeRef.current = now;
    waitingForReactionRef.current = true;

    console.log('📝 State updated:', {
      activeDevice: device.mac,
      activationTime: now,
      waitingForReaction: true
    });

    // Enviar comando para ativar dispositivo (LED)
    sendCommandToDevice(device.mac, { command: "timedactivate" });
    console.log('📤 Command sent to device:', device.mac);

    toast({
      title: "🎯 Dispositivo Ativado!",
      description: `Reaja rapidamente ao ${device.name || device.mac}!`
    });

    // Timeout para reação
    reactionTimeoutRef.current = setTimeout(() => {
      if (waitingForReactionRef.current && activeDeviceRef.current?.mac === device.mac) {
        handleMissedReaction();
      }
    }, reactionTimeout);

  }, [getRandomDevice, sendCommandToDevice, reactionTimeout, toast]);
  
  // Processar reação (chamado quando recebe {"pass": true} - igual ao modo velocidade)
  const processReaction = useCallback((deviceMac, timestamp) => {
    console.log('🎮 processReaction called:', { deviceMac, timestamp, gameState: gameStateRef.current });

    // Se não estiver em jogo, ignorar
    if (gameStateRef.current !== 'running') {
      console.log('❌ Game not running, ignoring reaction');
      return;
    }

    // Debug detalhado
    console.log('🔍 Debug state:', {
      waitingForReaction: waitingForReactionRef.current,
      activeDevice: activeDeviceRef.current,
      deviceMac,
      activeDeviceMac: activeDeviceRef.current?.mac,
      isCorrectDevice: activeDeviceRef.current?.mac === deviceMac
    });

    // Verificar se é o dispositivo ativo (LED aceso)
    if (waitingForReactionRef.current && activeDeviceRef.current && activeDeviceRef.current.mac === deviceMac) {
      // SUCESSO: Passou no sensor correto quando LED estava aceso
      console.log('✅ SUCCESS: Correct sensor activated');
    } else {
      // FALHA: Passou em sensor quando não deveria (LED não estava aceso ou sensor errado)
      console.log('❌ MISS: Wrong sensor or no active sensor');

      const reaction = {
        id: Date.now(),
        device: { mac: deviceMac, name: `Sensor ${deviceMac}` },
        activationTime: null,
        reactionTime: 0,
        timestamp,
        success: false,
        speed: 0,
        round: currentRound + 1,
        reason: waitingForReactionRef.current ? 'wrong_sensor' : 'no_active_sensor'
      };

      setReactions(prev => [...prev, reaction]);
      setMisses(prev => prev + 1);

      toast({
        title: "❌ Falha!",
        description: waitingForReactionRef.current ?
          `Sensor errado! Aguarde o sensor correto` :
          "Nenhum sensor ativo! Aguarde a ativação.",
        variant: "destructive"
      });

      // Continuar o jogo normalmente - não interromper
      return;
    }

    const reactionTime = timestamp - activationTimeRef.current;
    const speed = (distance / (reactionTime / 1000)) * 3.6; // km/h

    console.log('📊 Calculating success:', {
      timestamp,
      activationTime: activationTimeRef.current,
      reactionTime,
      speed,
      distance
    });

    const reaction = {
      id: Date.now(),
      device: activeDeviceRef.current,
      activationTime: activationTimeRef.current,
      reactionTime,
      timestamp,
      success: true,
      speed: speed,
      round: currentRound + 1
    };

    console.log('💾 Saving reaction:', reaction);

    setReactions(prev => [...prev, reaction]);
    setSuccesses(prev => prev + 1);
    setWaitingForReaction(false);
    setActiveDevice(null);

    // Atualizar refs
    waitingForReactionRef.current = false;
    activeDeviceRef.current = null;

    console.log('🧹 Cleaned up state after success');

    // Limpar timeout de reação
    if (reactionTimeoutRef.current) {
      clearTimeout(reactionTimeoutRef.current);
      reactionTimeoutRef.current = null;
    }

    toast({
      title: "✅ Sucesso!",
      description: `Tempo de reação: ${reactionTime}ms | Velocidade: ${speed.toFixed(1)} km/h`
    });

    // Próxima ativação
    console.log('➡️ Scheduling next activation');
    setCurrentRound(prev => prev + 1);
    scheduleNextActivation();

  }, [distance, currentRound, toast, scheduleNextActivation]);
  
  // Processar reação perdida
  const handleMissedReaction = useCallback(() => {
    if (!activeDeviceRef.current) return;
    
    const reaction = {
      id: Date.now(),
      device: activeDeviceRef.current,
      activationTime: activationTimeRef.current,
      reactionTime: reactionTimeout,
      timestamp: null,
      success: false,
      speed: 0,
      round: currentRound + 1
    };
    
    setReactions(prev => [...prev, reaction]);
    setMisses(prev => prev + 1);
    setWaitingForReaction(false);
    setActiveDevice(null);
    
    toast({ 
      title: "❌ Falta!", 
      description: `Não reagiu a tempo (${reactionTimeout}ms)`,
      variant: "destructive"
    });
    
    // Próxima ativação
    setCurrentRound(prev => prev + 1);
    scheduleNextActivation();
    
  }, [reactionTimeout, currentRound, toast, scheduleNextActivation]);

  // Terminar jogo (declarado antes para evitar erro de referência)
  const endGame = useCallback(() => {
    setGameState('finished');
    setWaitingForReaction(false);
    setActiveDevice(null);

    // Limpar timeouts
    if (reactionTimeoutRef.current) clearTimeout(reactionTimeoutRef.current);
    if (nextActivationTimeoutRef.current) clearTimeout(nextActivationTimeoutRef.current);

    const accuracy = reactions.length > 0 ? (successes / reactions.length) * 100 : 0;
    const avgReactionTime = reactions.filter(r => r.success).length > 0
      ? reactions.filter(r => r.success).reduce((sum, r) => sum + r.reactionTime, 0) / reactions.filter(r => r.success).length
      : 0;
    const maxSpeed = reactions.length > 0 ? Math.max(...reactions.map(r => r.speed)) : 0;

    // Salvar jogo no histórico do participante
    if (participantName && addReactionGameToParticipant) {
      const gameResult = {
        id: Date.now(),
        type: 'reaction',
        participantName,
        date: new Date().toISOString(),
        gameTime,
        totalRounds: currentRound,
        successes,
        misses,
        accuracy,
        averageReactionTime: avgReactionTime,
        maxSpeed,
        reactions: [...reactions],
        settings: {
          reactionTimeout,
          nextActivationDelay,
          distance,
          totalRounds,
          maxGameTime
        }
      };

      addReactionGameToParticipant(participantName, gameResult);
    }

    toast({
      title: "🏁 Jogo Terminado!",
      description: `Sucessos: ${successes} | Faltas: ${misses} | Precisão: ${accuracy.toFixed(1)}%`
    });
  }, [reactions, successes, misses, gameTime, currentRound, participantName, addReactionGameToParticipant,
      reactionTimeout, nextActivationDelay, distance, totalRounds, maxGameTime, toast]);

  // Agendar próxima ativação
  const scheduleNextActivation = useCallback(() => {
    console.log('⏰ scheduleNextActivation called:', {
      gameState: gameStateRef.current,
      currentRound,
      totalRounds,
      shouldContinue: currentRound + 1 < totalRounds
    });

    if (gameStateRef.current !== 'running') {
      console.log('❌ Game not running, not scheduling');
      return;
    }

    // Verificar se deve continuar
    if (currentRound + 1 >= totalRounds) {
      console.log('🏁 Game should end, calling endGame');
      endGame();
      return;
    }

    console.log('⏳ Scheduling next activation in', nextActivationDelay, 'ms');
    nextActivationTimeoutRef.current = setTimeout(() => {
      console.log('🔄 Timeout triggered, calling activateNextDevice');
      activateNextDevice();
    }, nextActivationDelay);

  }, [currentRound, totalRounds, nextActivationDelay, activateNextDevice, endGame]);
  
  // Iniciar jogo
  const startGame = useCallback(() => {
    console.log('🎮 startGame called', { mqttStatus, participantName, devices });

    if (mqttStatus !== 'Connected') {
      toast({ title: "Erro", description: "Conecte ao MQTT primeiro!", variant: "destructive" });
      return;
    }

    if (!participantName) {
      toast({ title: "Erro", description: "Selecione um participante primeiro!", variant: "destructive" });
      return;
    }

    const activeDevices = devices.filter(d => d.active);
    console.log('🔍 Active devices:', activeDevices);

    if (activeDevices.length === 0) {
      toast({ title: "Erro", description: "Nenhum dispositivo ativo!", variant: "destructive" });
      return;
    }

    // Enviar comando inicial timedactivate para todos os dispositivos ativos
    console.log('📤 Sending timedactivate to devices:', activeDevices.map(d => d.mac));
    activeDevices.forEach(device => {
      console.log(`📤 Sending to ${device.mac}:`, { command: "timedactivate" });
      sendCommandToDevice(device.mac, { command: "timedactivate" });
    });

    // Reset do estado
    setGameState('running');
    setCurrentRound(0);
    setGameTime(0);
    setSuccesses(0);
    setMisses(0);
    setReactions([]);
    setActiveDevice(null);
    setWaitingForReaction(false);

    console.log('✅ Game state set to running');

    toast({
      title: "🎮 Jogo Iniciado!",
      description: `Comando timedactivate enviado para ${activeDevices.length} dispositivos. Prepare-se para reagir!`
    });

    // Primeira ativação após delay
    console.log('⏰ Setting timeout for first activation');
    setTimeout(() => {
      console.log('🎯 Calling activateNextDevice');
      activateNextDevice();
    }, 2000);

  }, [mqttStatus, devices, participantName, toast, activateNextDevice, sendCommandToDevice]);
  
  // Parar jogo
  const stopGame = useCallback(() => {
    setGameState('idle');
    setWaitingForReaction(false);
    setActiveDevice(null);
    
    // Limpar timeouts
    if (reactionTimeoutRef.current) clearTimeout(reactionTimeoutRef.current);
    if (nextActivationTimeoutRef.current) clearTimeout(nextActivationTimeoutRef.current);
    
    toast({ title: "🛑 Jogo Parado", description: "Jogo interrompido pelo utilizador" });
  }, [toast]);

  // Reset do jogo
  const resetGame = useCallback(() => {
    setGameState('idle');
    setCurrentRound(0);
    setGameTime(0);
    setSuccesses(0);
    setMisses(0);
    setReactions([]);
    setActiveDevice(null);
    setWaitingForReaction(false);

    // Limpar timeouts
    if (reactionTimeoutRef.current) clearTimeout(reactionTimeoutRef.current);
    if (nextActivationTimeoutRef.current) clearTimeout(nextActivationTimeoutRef.current);

  }, []);

  // Configurar dispositivos
  const configureDevices = useCallback(() => {
    const activeDevices = devices.filter(d => d.active);
    if (activeDevices.length === 0) {
      toast({ title: "Erro", description: "Nenhum dispositivo ativo para configurar", variant: "destructive" });
      return;
    }

    const config = {
      command: "configure",
      distancethreshold: 150, // Valor fixo para modo reação
      globaltimeout: reactionTimeout,
      lastresorttimeout: nextActivationDelay
    };

    activeDevices.forEach(device => {
      sendCommandToDevice(device.mac, config);
    });

    toast({
      title: "✅ Configuração Enviada",
      description: `Configurados ${activeDevices.length} dispositivos para modo reação`
    });
  }, [devices, reactionTimeout, nextActivationDelay, sendCommandToDevice, toast]);
  
  return {
    // Estado do jogo
    gameState,
    currentRound,
    totalRounds,
    gameTime,
    maxGameTime,
    
    // Configurações
    reactionTimeout,
    setReactionTimeout,
    nextActivationDelay,
    setNextActivationDelay,
    distance,
    setDistance,
    setTotalRounds,
    setMaxGameTime,
    
    // Estado atual
    activeDevice,
    waitingForReaction,
    
    // Contadores
    successes,
    misses,
    reactions,
    
    // Ações
    startGame,
    stopGame,
    resetGame,
    processReaction,
    configureDevices,
    
    // Estatísticas
    accuracy: reactions.length > 0 ? (successes / reactions.length) * 100 : 0,
    averageReactionTime: reactions.filter(r => r.success).length > 0 
      ? reactions.filter(r => r.success).reduce((sum, r) => sum + r.reactionTime, 0) / reactions.filter(r => r.success).length 
      : 0,
    maxSpeed: reactions.length > 0 ? Math.max(...reactions.map(r => r.speed)) : 0
  };
};
