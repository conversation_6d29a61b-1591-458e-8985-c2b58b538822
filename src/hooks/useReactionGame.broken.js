import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

export const useReactionGame = ({ devices, sendCommandToDevice, mqttStatus, participantName, addReactionGameToParticipant }) => {
  const { toast } = useToast();
  
  // Estados básicos do jogo
  const [gameState, setGameState] = useState('idle'); // 'idle', 'running', 'finished'
  const [currentRound, setCurrentRound] = useState(0);
  const [successes, setSuccesses] = useState(0);
  const [misses, setMisses] = useState(0);
  const [reactions, setReactions] = useState([]);
  const [activeDevice, setActiveDevice] = useState(null);
  
  // Configurações simples
  const distance = 20; // metros fixo
  const totalRounds = 10; // 10 rondas fixo

  // Função simples para processar reação
  const processReaction = useCallback((deviceMac, timestamp) => {
    console.log('🎮 Simple processReaction:', { deviceMac, timestamp, gameState, activeDevice });
    
    // Se não estiver jogando, ignorar
    if (gameState !== 'running') {
      console.log('❌ Game not running');
      return;
    }

    // Se é o dispositivo ativo, é sucesso
    if (activeDevice && activeDevice.mac === deviceMac) {
      console.log('✅ SUCCESS!');
      
      const reactionTime = timestamp - activeDevice.activatedAt;
      const speed = (distance / (reactionTime / 1000)) * 3.6; // km/h
      
      const reaction = {
        id: Date.now(),
        device: activeDevice,
        activationTime: activeDevice.activatedAt,
        reactionTime,
        timestamp,
        success: true,
        speed: speed,
        round: currentRound + 1
      };

      setReactions(prev => [...prev, reaction]);
      setSuccesses(prev => prev + 1);
      setActiveDevice(null);

      toast({
        title: "✅ Sucesso!",
        description: `Tempo: ${reactionTime}ms | Velocidade: ${speed.toFixed(1)} km/h`
      });

      // Próxima ronda
      setCurrentRound(prev => prev + 1);
      
      // Ativar próximo dispositivo após delay
      setTimeout(() => {
        activateNextDevice();
      }, 2000);

    } else {
      console.log('❌ MISS - wrong device or no active device');
      
      const reaction = {
        id: Date.now(),
        device: { mac: deviceMac, name: `Sensor ${deviceMac}` },
        activationTime: null,
        reactionTime: 0,
        timestamp,
        success: false,
        speed: 0,
        round: currentRound + 1
      };

      setReactions(prev => [...prev, reaction]);
      setMisses(prev => prev + 1);

      toast({
        title: "❌ Falha!",
        description: "Sensor errado ou nenhum sensor ativo!",
        variant: "destructive"
      });
    }
  }, [gameState, activeDevice, currentRound, distance, toast]);

  // Função simples para ativar próximo dispositivo
  const activateNextDevice = useCallback(() => {
    console.log('🎯 Activating next device');
    
    if (gameState !== 'running') {
      console.log('❌ Game not running');
      return;
    }

    if (currentRound >= totalRounds) {
      console.log('🏁 Game finished');
      setGameState('finished');
      return;
    }

    const activeDevices = devices.filter(d => d.active);
    if (activeDevices.length === 0) {
      toast({ title: "Erro", description: "Nenhum dispositivo ativo!", variant: "destructive" });
      return;
    }

    const device = activeDevices[Math.floor(Math.random() * activeDevices.length)];
    const now = Date.now();
    
    const deviceWithTime = {
      ...device,
      activatedAt: now
    };

    setActiveDevice(deviceWithTime);
    
    console.log('⚡ Device activated:', deviceWithTime);

    // Enviar comando para ativar LED
    sendCommandToDevice(device.mac, { command: "timedactivate" });

    toast({
      title: "🎯 Dispositivo Ativado!",
      description: `Reaja ao ${device.name || device.mac}!`
    });

  }, [gameState, currentRound, totalRounds, devices, sendCommandToDevice, toast]);

  // Função simples para iniciar jogo
  const startGame = useCallback(() => {
    console.log('🎮 Starting simple game');
    
    if (mqttStatus !== 'Connected') {
      toast({ title: "Erro", description: "Conecte ao MQTT primeiro!", variant: "destructive" });
      return;
    }

    if (!participantName) {
      toast({ title: "Erro", description: "Selecione um participante!", variant: "destructive" });
      return;
    }

    const activeDevices = devices.filter(d => d.active);
    if (activeDevices.length === 0) {
      toast({ title: "Erro", description: "Nenhum dispositivo ativo!", variant: "destructive" });
      return;
    }

    // Reset do estado
    setGameState('running');
    setCurrentRound(0);
    setSuccesses(0);
    setMisses(0);
    setReactions([]);
    setActiveDevice(null);

    toast({
      title: "🎮 Jogo Iniciado!",
      description: "Prepare-se para reagir!"
    });

    // Primeira ativação após delay
    setTimeout(() => {
      activateNextDevice();
    }, 2000);

  }, [mqttStatus, participantName, devices, toast, activateNextDevice]);

  // Função simples para parar jogo
  const stopGame = useCallback(() => {
    setGameState('idle');
    setActiveDevice(null);
    toast({ title: "🛑 Jogo Parado", description: "Jogo interrompido" });
  }, [toast]);

  // Função simples para resetar jogo
  const resetGame = useCallback(() => {
    setGameState('idle');
    setCurrentRound(0);
    setSuccesses(0);
    setMisses(0);
    setReactions([]);
    setActiveDevice(null);
  }, []);

  return {
    // Estado
    gameState,
    currentRound,
    totalRounds,
    successes,
    misses,
    reactions,
    activeDevice,
    
    // Ações
    startGame,
    stopGame,
    resetGame,
    processReaction,
    
    // Estatísticas
    accuracy: reactions.length > 0 ? (successes / reactions.length) * 100 : 0,
    averageReactionTime: reactions.filter(r => r.success).length > 0 
      ? reactions.filter(r => r.success).reduce((sum, r) => sum + r.reactionTime, 0) / reactions.filter(r => r.success).length 
      : 0,
    maxSpeed: reactions.length > 0 ? Math.max(...reactions.map(r => r.speed)) : 0,
    
    // Configurações
    settings: {
      distance,
      totalRounds
    }
  };
};
