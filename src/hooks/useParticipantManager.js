import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { safeLocalStorageGet, safeLocalStorageSet } from '@/lib/storage';
import { validateParticipantName } from '@/lib/validation';

export const useParticipantManager = () => {
    const [participants, setParticipants] = useState({});
    const { toast } = useToast();

    useEffect(() => {
        // Migração: mover participantes da chave antiga para a nova
        const oldParticipants = safeLocalStorageGet('speedParticipants', {});
        const newParticipants = safeLocalStorageGet('app_participants', {});

        console.log('🔄 Checking participant migration:', {
            oldCount: Object.keys(oldParticipants).length,
            newCount: Object.keys(newParticipants).length
        });

        // Se há participantes na chave antiga e nenhum na nova, migrar
        if (Object.keys(oldParticipants).length > 0 && Object.keys(newParticipants).length === 0) {
            console.log('🔄 Migrating participants from speedParticipants to app_participants:', oldParticipants);
            safeLocalStorageSet('app_participants', oldParticipants);
            setParticipants(oldParticipants);
        }
        // Se há participantes em ambas, mesclar (prioridade para a nova)
        else if (Object.keys(oldParticipants).length > 0 && Object.keys(newParticipants).length > 0) {
            const mergedParticipants = { ...oldParticipants, ...newParticipants };
            console.log('🔄 Merging participants:', { oldParticipants, newParticipants, mergedParticipants });
            safeLocalStorageSet('app_participants', mergedParticipants);
            setParticipants(mergedParticipants);
        }
        // Caso normal: carregar da nova chave
        else {
            console.log('🔄 useParticipantManager loading participants:', { newParticipants, participantCount: Object.keys(newParticipants).length });
            setParticipants(newParticipants);
        }
    }, []);

    useEffect(() => {
        console.log('💾 useParticipantManager saving participants:', {
            participants,
            participantCount: Object.keys(participants).length,
            participantNames: Object.keys(participants),
            localStorage_before: safeLocalStorageGet('app_participants', {})
        });

        const saveResult = safeLocalStorageSet('app_participants', participants);
        console.log('💾 Save result:', saveResult);

        // Verificar se foi salvo corretamente
        const savedData = safeLocalStorageGet('app_participants', {});
        console.log('💾 Verification - data after save:', {
            savedData,
            savedCount: Object.keys(savedData).length,
            matches: JSON.stringify(savedData) === JSON.stringify(participants)
        });
    }, [participants]);
    
    const addParticipant = (name) => {
        const nameValidation = validateParticipantName(name);

        if (!nameValidation.isValid) {
            toast({
                title: 'Erro',
                description: nameValidation.error,
                variant: 'destructive',
            });
            return;
        }

        if(participants[nameValidation.value]) {
            toast({
                title: 'Erro',
                description: `Participante "${nameValidation.value}" já existe.`,
                variant: 'destructive',
            });
            return;
        }

        setParticipants(prev => ({ ...prev, [nameValidation.value]: { runs: [], reactionGames: [], age: null, photo: null } }));
        toast({
            title: 'Participante Criado!',
            description: `${nameValidation.value} foi adicionado.`,
        });
    };

    const updateParticipant = (oldName, newName, newAge, newPhoto) => {
        const nameValidation = validateParticipantName(newName);

        if (!nameValidation.isValid) {
            toast({
                title: 'Erro',
                description: nameValidation.error,
                variant: 'destructive',
            });
            return;
        }

        setParticipants(prev => {
            const allParticipants = { ...prev };
            const participantData = allParticipants[oldName];

            if (!participantData || (oldName !== nameValidation.value && allParticipants[nameValidation.value])) {
                return prev;
            }

            delete allParticipants[oldName];

            allParticipants[nameValidation.value] = {
                ...participantData,
                runs: participantData.runs ? participantData.runs.map(run => ({...run, participantName: nameValidation.value})) : [],
                reactionGames: participantData.reactionGames ? participantData.reactionGames.map(game => ({...game, participantName: nameValidation.value})) : [],
                age: newAge || null,
                photo: newPhoto || null,
            };

            return allParticipants;
        });
        toast({ title: "Sucesso!", description: "Dados do participante atualizados." });
    };

    const addRunToParticipant = (participantName, newRun) => {
        setParticipants(prev => {
            const participantData = prev[participantName] || { runs: [], reactionGames: [], age: null, photo: null };
            const updatedRuns = [...participantData.runs, newRun];
            return {
                ...prev,
                [participantName]: { ...participantData, runs: updatedRuns }
            };
        });
    };

    const addReactionGameToParticipant = (participantName, game) => {
        console.log('🎮 addReactionGameToParticipant called:', { participantName, game });

        setParticipants(prev => {
            const participantData = prev[participantName] || { runs: [], reactionGames: [], age: null, photo: null };
            const updatedGames = [...participantData.reactionGames, game];
            const newParticipants = {
                ...prev,
                [participantName]: { ...participantData, reactionGames: updatedGames }
            };

            console.log('🎮 Updated participants:', {
                before: prev,
                after: newParticipants,
                gameCount: updatedGames.length
            });

            // Forçar salvamento imediato
            setTimeout(() => {
                safeLocalStorageSet('app_participants', newParticipants);
                console.log('🎮 Forced immediate save to localStorage');
            }, 100);

            return newParticipants;
        });
        toast({
            title: 'Jogo Salvo!',
            description: `Jogo de reação salvo para ${participantName}.`,
        });
    };

    return {
        participants,
        addParticipant,
        updateParticipant,
        addRunToParticipant,
        addReactionGameToParticipant,
    };
};