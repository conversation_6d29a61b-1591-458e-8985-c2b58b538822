import { useState, useCallback, useRef, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { safeLocalStorageGet, safeLocalStorageSet } from '@/lib/storage';

export const useReactionGame = ({ devices, toggleDeviceActive, sendCommandToDevice, mqttStatus, participantName, addReactionGameToParticipant }) => {
  const { toast } = useToast();

  // Estado para forçar re-render quando devices mudam
  const [devicesVersion, setDevicesVersion] = useState(0);

  // Estado para forçar re-render quando configurações mudam
  const [configVersion, setConfigVersion] = useState(0);

  // ===== GERADOR DE ID ÚNICO =====
  const idCounterRef = useRef(0);
  const generateUniqueId = useCallback(() => {
    idCounterRef.current += 1;
    return `${Date.now()}-${idCounterRef.current}`;
  }, []);

  // ===== CONTROLE DE ATIVAÇÃO =====
  const activationInProgressRef = useRef(false);
  const nextActivationTimeoutRef = useRef(null);
  const activateNextDeviceRef = useRef(null);

  // Função para agendar próxima ativação
  const scheduleNextActivation = useCallback((delay = 0) => {
    console.log('⏰ Scheduling next activation in', delay, 'ms');

    // Limpar timeout anterior se existir
    if (nextActivationTimeoutRef.current) {
      clearTimeout(nextActivationTimeoutRef.current);
      nextActivationTimeoutRef.current = null;
    }

    nextActivationTimeoutRef.current = setTimeout(() => {
      console.log('🔄 Scheduled activation triggered');
      if (activateNextDeviceRef.current) {
        activateNextDeviceRef.current();
      }
    }, delay);
  }, []);

  // Ref para manter sempre a versão mais atual dos devices
  const devicesRef = useRef(devices);
  devicesRef.current = devices;

  // Estados básicos do jogo (movido para antes dos useEffects)
  const [gameState, setGameState] = useState('idle'); // 'idle', 'running', 'finished'
  const [currentRound, setCurrentRound] = useState(0);
  const [activeDevice, setActiveDevice] = useState(null);

  // Atualizar dispositivos e gerenciar estado do jogo (movido para depois da definição de activateNextDevice)
  const currentRoundRef = useRef(0);

  // Refs para todas as configurações de jogo (para usar em callbacks e evitar closure)
  const totalRoundsRef = useRef(10);
  const maxGameTimeRef = useRef(300);
  const distanceRef = useRef(20);
  const nextActivationDelayRef = useRef(1500);
  const allowSameDeviceRef = useRef(false);
  const distanceThresholdRef = useRef(150);
  const globalTimeoutRef = useRef(8000);
  const lastResortTimeoutRef = useRef(2000);
  const [successes, setSuccesses] = useState(0);
  const [misses, setMisses] = useState(0);
  const [reactions, setReactions] = useState([]);
  const [gameTime, setGameTime] = useState(0);
  const [waitingForReaction, setWaitingForReaction] = useState(false);
  const [timeoutTimer, setTimeoutTimer] = useState(null);

  // ===== CONFIGURAÇÕES DE JOGO (com persistência) =====
  const [totalRounds, setTotalRounds] = useState(() =>
    safeLocalStorageGet('reactionGame_totalRounds', 10)
  );
  const [maxGameTime, setMaxGameTime] = useState(() =>
    safeLocalStorageGet('reactionGame_maxGameTime', 300)
  );
  const [distance, setDistance] = useState(() =>
    safeLocalStorageGet('reactionGame_distance', 20)
  );
  const [nextActivationDelay, setNextActivationDelay] = useState(() =>
    safeLocalStorageGet('reactionGame_nextActivationDelay', 1500)
  );
  const [allowSameDevice, setAllowSameDevice] = useState(() =>
    safeLocalStorageGet('reactionGame_allowSameDevice', false)
  );

  // Log quando allowSameDevice muda - removido para evitar spam

  // ===== CONFIGURAÇÕES DE DISPOSITIVOS (com persistência) =====
  const [distanceThreshold, setDistanceThreshold] = useState(() =>
    safeLocalStorageGet('reactionGame_distanceThreshold', 150)
  );
  const [globalTimeout, setGlobalTimeout] = useState(() =>
    safeLocalStorageGet('reactionGame_globalTimeout', 8000)
  );
  const [lastResortTimeout, setLastResortTimeout] = useState(() =>
    safeLocalStorageGet('reactionGame_lastResortTimeout', 2000)
  );

  // Persistir configurações de jogo quando mudarem
  useEffect(() => {
    safeLocalStorageSet('reactionGame_totalRounds', totalRounds);
    totalRoundsRef.current = totalRounds; // Atualizar ref para usar em callbacks
    setConfigVersion(prev => prev + 1); // Forçar re-render
  }, [totalRounds]);

  useEffect(() => {
    safeLocalStorageSet('reactionGame_maxGameTime', maxGameTime);
    maxGameTimeRef.current = maxGameTime; // Atualizar ref para usar em callbacks
    setConfigVersion(prev => prev + 1); // Forçar re-render
  }, [maxGameTime]);

  useEffect(() => {
    safeLocalStorageSet('reactionGame_distance', distance);
    distanceRef.current = distance; // Atualizar ref para usar em callbacks
    setConfigVersion(prev => prev + 1); // Forçar re-render
  }, [distance]);

  useEffect(() => {
    safeLocalStorageSet('reactionGame_nextActivationDelay', nextActivationDelay);
    nextActivationDelayRef.current = nextActivationDelay; // Atualizar ref para usar em callbacks
    setConfigVersion(prev => prev + 1); // Forçar re-render
  }, [nextActivationDelay]);

  useEffect(() => {
    safeLocalStorageSet('reactionGame_allowSameDevice', allowSameDevice);
    allowSameDeviceRef.current = allowSameDevice; // Atualizar ref para usar em callbacks
    setConfigVersion(prev => prev + 1); // Forçar re-render
  }, [allowSameDevice]);

  // Persistir configurações de dispositivos quando mudarem
  useEffect(() => {
    safeLocalStorageSet('reactionGame_distanceThreshold', distanceThreshold);
    distanceThresholdRef.current = distanceThreshold; // Atualizar ref para usar em callbacks
    setConfigVersion(prev => prev + 1); // Forçar re-render
    console.log('💾 Saved distanceThreshold:', distanceThreshold, '- Ref updated');
  }, [distanceThreshold]);

  useEffect(() => {
    safeLocalStorageSet('reactionGame_globalTimeout', globalTimeout);
    globalTimeoutRef.current = globalTimeout; // Atualizar ref para usar em callbacks
    setConfigVersion(prev => prev + 1); // Forçar re-render
    console.log('💾 Saved globalTimeout:', globalTimeout, '- Ref updated');
  }, [globalTimeout]);

  useEffect(() => {
    safeLocalStorageSet('reactionGame_lastResortTimeout', lastResortTimeout);
    lastResortTimeoutRef.current = lastResortTimeout; // Atualizar ref para usar em callbacks
    setConfigVersion(prev => prev + 1); // Forçar re-render
    console.log('💾 Saved lastResortTimeout:', lastResortTimeout, '- Ref updated');
  }, [lastResortTimeout]);

  // Validação de configurações
  useEffect(() => {
    // Validação: lastResortTimeout deve ser menor que globalTimeout
    if (lastResortTimeout >= globalTimeout) {
      const newLastResortTimeout = Math.max(1500, globalTimeout - 500);
      setLastResortTimeout(newLastResortTimeout);
    }
  }, [distanceThreshold, globalTimeout, lastResortTimeout]);
  
  // Refs para manter estado atual
  const gameStateRef = useRef('idle');
  const activeDeviceRef = useRef(null);
  const gameTimerRef = useRef(null);
  const timeoutTimerRef = useRef(null);

  // Atualizar refs quando estados mudam
  gameStateRef.current = gameState;
  activeDeviceRef.current = activeDevice;
  timeoutTimerRef.current = timeoutTimer;

  // Timer do jogo
  useEffect(() => {
    if (gameState === 'running') {
      gameTimerRef.current = setInterval(() => {
        setGameTime(prev => {
          const newTime = prev + 100;
          const currentMaxGameTime = maxGameTimeRef.current;
          if (newTime >= currentMaxGameTime * 1000) { // maxGameTime está em segundos
            console.log('⏰ Game time limit reached');
            setGameState('finished');
            toast({
              title: "⏰ Tempo Esgotado!",
              description: `Jogo terminou após ${currentMaxGameTime} segundos`
            });
          }
          return newTime;
        });
      }, 100);
    } else {
      if (gameTimerRef.current) {
        clearInterval(gameTimerRef.current);
        gameTimerRef.current = null;
      }
    }

    return () => {
      if (gameTimerRef.current) {
        clearInterval(gameTimerRef.current);
      }
    };
  }, [gameState, maxGameTime, toast]);

  // Ref para controlar se o jogo já foi salvo
  const gameSavedRef = useRef(false);

  // Salvar jogo quando termina (apenas uma vez)
  useEffect(() => {
    if (gameState === 'finished' && participantName && reactions.length > 0 && !gameSavedRef.current) {
      gameSavedRef.current = true; // Marcar como salvo para evitar loop

      const gameData = {
        id: generateUniqueId(),
        date: new Date().toISOString(),
        participantName,
        totalRounds,
        gameTime: gameTime / 1000, // converter para segundos
        successes,
        misses,
        reactions,
        accuracy: reactions.length > 0 ? (successes / reactions.length) * 100 : 0,
        averageReactionTime: reactions.filter(r => r.success).length > 0
          ? reactions.filter(r => r.success).reduce((sum, r) => sum + r.reactionTime, 0) / reactions.filter(r => r.success).length
          : 0,
        maxSpeed: reactions.length > 0 ? Math.max(...reactions.map(r => r.speed)) : 0,
        settings: {
          totalRounds,
          maxGameTime,
          distance,
          nextActivationDelay,
          allowSameDevice,
          distanceThreshold,
          globalTimeout,
          lastResortTimeout
        }
      };

      console.log('💾 Saving reaction game to participant (ONCE):', { participantName, gameData });
      addReactionGameToParticipant(participantName, gameData);

      toast({
        title: "💾 Jogo Salvo!",
        description: `Estatísticas salvas para ${participantName}`
      });
    }
  }, [gameState, participantName, reactions.length, successes, misses, gameTime, totalRounds, maxGameTime, distance, nextActivationDelay, allowSameDevice, distanceThreshold, globalTimeout, lastResortTimeout, addReactionGameToParticipant, toast]);

  // Função manual para salvar jogo (para debug/teste)
  const saveGameManually = useCallback(() => {
    if (!participantName) {
      toast({
        title: "Erro",
        description: "Selecione um participante primeiro",
        variant: "destructive"
      });
      return;
    }

    const gameData = {
      id: generateUniqueId(),
      date: new Date().toISOString(),
      participantName,
      totalRounds,
      gameTime: gameTime / 1000,
      successes,
      misses,
      reactions,
      accuracy: reactions.length > 0 ? (successes / reactions.length) * 100 : 0,
      averageReactionTime: reactions.filter(r => r.success).length > 0
        ? reactions.filter(r => r.success).reduce((sum, r) => sum + r.reactionTime, 0) / reactions.filter(r => r.success).length
        : 0,
      maxSpeed: reactions.length > 0 ? Math.max(...reactions.map(r => r.speed)) : 0,
      settings: {
        totalRounds,
        maxGameTime,
        distance,
        nextActivationDelay,
        allowSameDevice,
        distanceThreshold,
        globalTimeout,
        lastResortTimeout
      }
    };

    console.log('🔧 Manual save - gameData:', gameData);
    addReactionGameToParticipant(participantName, gameData);

    toast({
      title: "💾 Jogo Salvo Manualmente!",
      description: `Estatísticas salvas para ${participantName}`
    });
  }, [participantName, totalRounds, gameTime, successes, misses, reactions, maxGameTime, distance, nextActivationDelay, allowSameDevice, distanceThreshold, globalTimeout, lastResortTimeout, addReactionGameToParticipant, toast]);

  // Função para limpar dados corrompidos (emergência)
  const clearCorruptedData = useCallback(() => {
    if (confirm('⚠️ ATENÇÃO: Isso vai apagar TODOS os dados de participantes. Continuar?')) {
      safeLocalStorageSet('app_participants', {});
      // Limpar também dados específicos que podem estar corrompidos
      localStorage.removeItem('app_participantName');
      localStorage.removeItem('speedParticipants');
      window.location.reload();
    }
  }, []);

  // Função para debug do localStorage
  const debugLocalStorage = useCallback(() => {
    const appParticipants = safeLocalStorageGet('app_participants', {});
    const speedParticipants = safeLocalStorageGet('speedParticipants', {});
    const allKeys = Object.keys(localStorage).filter(key => key.includes('participant') || key.includes('app_'));

    console.log('🔍 DEBUG localStorage:', {
      app_participants: appParticipants,
      app_participants_count: Object.keys(appParticipants).length,
      speedParticipants: speedParticipants,
      speedParticipants_count: Object.keys(speedParticipants).length,
      allRelevantKeys: allKeys,
      allLocalStorageKeys: Object.keys(localStorage)
    });

    alert(`Debug info logged to console.
App participants: ${Object.keys(appParticipants).length}
Speed participants: ${Object.keys(speedParticipants).length}
All keys: ${allKeys.join(', ')}`);
  }, []);

  // Função para lidar com timeout (falha) - SEMPRE aguarda o tempo completo
  const handleTimeout = useCallback(() => {
    const timeoutTriggeredAt = new Date().toISOString();
    const currentGlobalTimeout = globalTimeoutRef.current;
    const currentLastResortTimeout = lastResortTimeoutRef.current;
    console.log('⏰ TIMEOUT: Device failed to respond at full timeout at', timeoutTriggeredAt);
    console.log('🔧 Timeout values when triggered:', { globalTimeout: currentGlobalTimeout, lastResortTimeout: currentLastResortTimeout });

    if (gameStateRef.current !== 'running') {
      console.log('⏰ TIMEOUT ignored - game not running');
      return;
    }

    const currentDevice = activeDeviceRef.current;
    if (!currentDevice) {
      return;
    }

    // Verificar se realmente passou o tempo completo
    const timeElapsed = Date.now() - currentDevice.activatedAt;
    console.log('⏰ Time check:', { timeElapsed, globalTimeout: currentGlobalTimeout, shouldTimeout: timeElapsed >= currentGlobalTimeout });

    // Se não passou o tempo completo, não fazer timeout ainda
    if (timeElapsed < currentGlobalTimeout) {
      console.log('⏰ Not enough time elapsed, waiting more...');
      return;
    }

    // Registrar como falha
    const failureReaction = {
      id: generateUniqueId(),
      device: { mac: currentDevice.mac, name: currentDevice.name || currentDevice.mac },
      activationTime: currentDevice.activatedAt || Date.now(),
      reactionTime: currentGlobalTimeout, // Tempo máximo
      timestamp: Date.now(),
      success: false,
      speed: 0,
      round: currentRoundRef.current + 1,
      type: 'timeout'
    };

    const newRound = currentRoundRef.current + 1;

    setReactions(prev => [...prev, failureReaction]);
    setMisses(prev => {
      const newMisses = prev + 1;
      console.log('❌ MISS: Timeout reached', { newRound, totalRounds, previousRound: currentRoundRef.current - 1, newMisses, previousMisses: prev });
      return newMisses;
    });
    setCurrentRound(newRound);
    currentRoundRef.current = newRound;

    toast({
      title: "❌ Timeout!",
      description: `${currentDevice.name || currentDevice.mac} não respondeu a tempo`,
      variant: "destructive"
    });

    // Limpar timer
    if (timeoutTimerRef.current) {
      clearTimeout(timeoutTimerRef.current);
      setTimeoutTimer(null);
    }

    // Verificar se jogo terminou
    const currentTotalRounds = totalRoundsRef.current;
    if (newRound >= currentTotalRounds) {
      console.log('🏁 Game finished after timeout', { newRound, totalRounds: currentTotalRounds });
      setGameState('finished');
      setActiveDevice(null);
      activationInProgressRef.current = false;
      toast({
        title: "🏁 Jogo Terminado!",
        description: `${currentTotalRounds} rondas | Sucessos: ${successes} | Falhas: ${misses + 1}`
      });
      return;
    }

    // Continuar jogo - agendar próximo dispositivo
    activationInProgressRef.current = false;
    const currentNextActivationDelay = nextActivationDelayRef.current;
    console.log('⏰ Scheduling next device after timeout, delay:', currentNextActivationDelay);
    scheduleNextActivation(currentNextActivationDelay);

  }, [globalTimeout, currentRound, totalRounds, nextActivationDelay, toast, scheduleNextActivation]);

  // Processar reação - SEMPRE conta como sucesso quando recebe telemetria
  const processReaction = useCallback((deviceMac, timestamp) => {
    console.log('🎮 processReaction called:', {
      deviceMac,
      timestamp,
      gameState: gameStateRef.current,
      activeDevice: activeDeviceRef.current?.mac,
      hasActiveDevice: !!activeDeviceRef.current
    });

    // Se não estiver em jogo, ignorar
    if (gameStateRef.current !== 'running') {
      console.log('❌ Game not running, ignoring');
      return;
    }

    // Verificar se há um dispositivo ativo esperando
    if (!activeDeviceRef.current) {
      console.log('❌ No active device waiting, ignoring telemetry');
      return;
    }

    // Verificar se a telemetria é do dispositivo ativo (opcional - pode aceitar qualquer)
    // if (activeDeviceRef.current.mac !== deviceMac) {
    //   console.log('❌ Telemetry from inactive device, ignoring');
    //   return;
    // }

    // SEMPRE conta como sucesso - qualquer sensor que envie telemetria
    console.log('✅ SUCCESS: Sensor detected from active game');

    // Limpar timer de timeout
    if (timeoutTimerRef.current) {
      clearTimeout(timeoutTimerRef.current);
      setTimeoutTimer(null);
      console.log('⏰ Timeout timer cleared');
    }

    const reactionTime = activeDeviceRef.current ? timestamp - activeDeviceRef.current.activatedAt : 0;
    const currentDistance = distanceRef.current;
    const speed = reactionTime > 0 ? (currentDistance / (reactionTime / 1000)) * 3.6 : 0; // km/h

    const reaction = {
      id: generateUniqueId(),
      device: { mac: deviceMac, name: activeDeviceRef.current?.name || deviceMac },
      activationTime: activeDeviceRef.current?.activatedAt || timestamp,
      reactionTime,
      timestamp,
      success: true,
      speed: speed,
      round: currentRoundRef.current + 1
    };

    const newRound = currentRoundRef.current + 1;

    setReactions(prev => [...prev, reaction]);
    setSuccesses(prev => {
      const newSuccesses = prev + 1;
      console.log('✅ SUCCESS processed', { newRound, totalRounds, previousRound: currentRoundRef.current - 1, newSuccesses, previousSuccesses: prev });
      return newSuccesses;
    });
    setCurrentRound(newRound);
    currentRoundRef.current = newRound;

    toast({
      title: "✅ Sucesso!",
      description: `${activeDeviceRef.current?.name || deviceMac} | Tempo: ${reactionTime}ms`
    });

    // Verificar se jogo terminou
    const currentTotalRounds = totalRoundsRef.current;
    if (newRound >= currentTotalRounds) {
      console.log('🏁 Game finished after success', { newRound, totalRounds: currentTotalRounds });
      setGameState('finished');
      setActiveDevice(null);

      // Limpar timer se existir
      if (timeoutTimerRef.current) {
        clearTimeout(timeoutTimerRef.current);
        setTimeoutTimer(null);
      }

      toast({
        title: "🏁 Jogo Terminado!",
        description: `${currentTotalRounds} rondas | Sucessos: ${successes + 1} | Falhas: ${misses}`
      });
      return;
    }

    // Continuar jogo - agendar próximo dispositivo
    activationInProgressRef.current = false;
    const currentNextActivationDelay = nextActivationDelayRef.current;
    console.log('⏰ Scheduling next device after success, delay:', currentNextActivationDelay);
    scheduleNextActivation(currentNextActivationDelay);

  }, [currentRound, distance, toast, scheduleNextActivation]);

  // Ativar próximo dispositivo (SEMPRE apenas 1 ativo)
  const activateNextDevice = useCallback(() => {
    console.log('🎯 activateNextDevice called', {
      gameState: gameStateRef.current,
      gameStateValue: gameState,
      currentActiveDevice: activeDeviceRef.current?.mac,
      activationInProgress: activationInProgressRef.current
    });

    if (gameStateRef.current !== 'running') {
      console.log('❌ Game not running', {
        gameStateRef: gameStateRef.current,
        gameState: gameState
      });
      return;
    }

    // Verificar se já há uma ativação em progresso (depois de verificar se o jogo está rodando)
    if (activationInProgressRef.current) {
      console.log('⚠️ Activation already in progress, skipping');
      return;
    }

    // Marcar como em progresso APENAS quando começar a ativação
    activationInProgressRef.current = true;

    // Usar a ref para garantir que temos a versão mais atual dos devices
    const currentDevices = devicesRef.current;
    const availableDevices = currentDevices.filter(d => d.active);

    if (availableDevices.length === 0) {
      setGameState('idle');
      setActiveDevice(null);
      toast({ title: "Erro", description: "Nenhum dispositivo ativo! Jogo parado.", variant: "destructive" });
      return;
    }

    // Guardar referência do dispositivo anterior para a lógica de seleção
    const previousDeviceMac = activeDeviceRef.current?.mac;

    let device;

    console.log('🎲 Device selection logic:', {
      allowSameDevice,
      availableDevicesCount: availableDevices.length,
      previousDevice: previousDeviceMac,
      availableDevices: availableDevices.map(d => d.mac)
    });

    const currentAllowSameDevice = allowSameDeviceRef.current;
    console.log('🔧 Current allowSameDevice value:', currentAllowSameDevice, typeof currentAllowSameDevice);

    if (currentAllowSameDevice || availableDevices.length === 1) {
      // Pode escolher qualquer dispositivo (incluindo o mesmo)
      device = availableDevices[Math.floor(Math.random() * availableDevices.length)];
      console.log('🎲 Random device selection (same allowed):', device.mac);
    } else {
      // Deve escolher um dispositivo diferente do anterior
      const otherDevices = availableDevices.filter(d => d.mac !== previousDeviceMac);

      console.log('🎲 Filtering devices:', {
        previousDeviceMac,
        otherDevicesCount: otherDevices.length,
        otherDevices: otherDevices.map(d => d.mac)
      });

      if (otherDevices.length === 0) {
        // Se só há um dispositivo, usar ele mesmo
        device = availableDevices[0];
        console.log('🎲 Only one device available:', device.mac);
      } else {
        device = otherDevices[Math.floor(Math.random() * otherDevices.length)];
        console.log('🎲 Different device selection:', device.mac, 'previous:', previousDeviceMac);
      }
    }
    const now = Date.now();

    const deviceWithTime = {
      ...device,
      activatedAt: now
    };

    console.log('⚡ Activating SINGLE device:', deviceWithTime.mac);
    console.log('🔧 Current timeout values at activation:', {
      globalTimeout,
      lastResortTimeout,
      distanceThreshold,
      deviceMac: device.mac,
      timestamp: new Date().toISOString()
    });

    setActiveDevice(deviceWithTime);
    activeDeviceRef.current = deviceWithTime;

    // Enviar comando APENAS para este dispositivo
    console.log('📤 Sending timedactivate command to device:', device.mac);
    sendCommandToDevice(device.mac, { command: "timedactivate" });

    // Iniciar timer de timeout - aguardar tempo total (globaltimeout + lastresorttimeout)
    const currentGlobalTimeout = globalTimeoutRef.current;
    const currentLastResortTimeout = lastResortTimeoutRef.current;
    const totalTimeout = currentGlobalTimeout + currentLastResortTimeout;
    const timer = setTimeout(() => {
      console.log('⏰ TIMEOUT TRIGGERED after', totalTimeout, 'ms at', new Date().toISOString());
      handleTimeout();
    }, totalTimeout);

    setTimeoutTimer(timer);
    console.log(`⏰ Timeout timer started: ${totalTimeout}ms total (LED aceso: 0-${currentGlobalTimeout}ms, LED pisca: ${currentGlobalTimeout}-${totalTimeout}ms)`);

    toast({
      title: "🎯 LED Ativado!",
      description: `Passe pelo sensor: ${device.name || device.mac} (${totalTimeout}ms total)`
    });

    // Marcar ativação como completa
    activationInProgressRef.current = false;

  }, [devices, sendCommandToDevice, toast, globalTimeout, handleTimeout, allowSameDevice, devicesVersion]);

  // Atualizar ref da função
  activateNextDeviceRef.current = activateNextDevice;

  // Ativar primeiro dispositivo quando jogo inicia (APENAS UMA VEZ)
  useEffect(() => {
    console.log('🔍 useEffect triggered:', {
      gameState,
      currentRound,
      activeDevice: !!activeDevice,
      activationInProgress: activationInProgressRef.current,
      shouldActivate: gameState === 'running' && currentRound === 0 && !activeDevice && !activationInProgressRef.current
    });

    if (gameState === 'running' && currentRound === 0 && !activeDevice && !activationInProgressRef.current) {
      console.log('🎮 Game started, scheduling first device activation');
      const delay = nextActivationDelayRef.current;
      console.log('⏰ Using delay:', delay);
      scheduleNextActivation(delay);
    }
  }, [gameState, currentRound, activeDevice, scheduleNextActivation]);

  // Atualizar versão dos dispositivos (SEM ativar dispositivos)
  useEffect(() => {
    setDevicesVersion(prev => prev + 1);
  }, [devices]);

  // Iniciar jogo
  const startGame = useCallback((setAppModeCallback) => {
    console.log('🎮 Starting game', { setAppModeCallback: !!setAppModeCallback });

    if (mqttStatus !== 'Connected') {
      toast({ title: "Erro", description: "Conecte ao MQTT primeiro!", variant: "destructive" });
      return;
    }

    if (!participantName) {
      toast({ title: "Erro", description: "Selecione um participante!", variant: "destructive" });
      return;
    }

    const availableDevices = devices.filter(d => d.active);
    if (availableDevices.length === 0) {
      toast({ title: "Erro", description: "Nenhum dispositivo ativo!", variant: "destructive" });
      return;
    }

    // Forçar modo reação
    if (setAppModeCallback) {
      console.log('🔄 Forcing reaction mode');
      setAppModeCallback('reaction');
    } else {
      console.log('❌ setAppModeCallback not provided');
    }

    // Reset do estado
    setCurrentRound(0);
    currentRoundRef.current = 0;

    // Garantir que todas as refs têm os valores atuais
    totalRoundsRef.current = totalRounds;
    maxGameTimeRef.current = maxGameTime;
    distanceRef.current = distance;
    nextActivationDelayRef.current = nextActivationDelay;
    allowSameDeviceRef.current = allowSameDevice;
    distanceThresholdRef.current = distanceThreshold;
    globalTimeoutRef.current = globalTimeout;
    lastResortTimeoutRef.current = lastResortTimeout;

    setSuccesses(0);
    setMisses(0);
    setReactions([]);
    setActiveDevice(null);
    gameSavedRef.current = false; // Reset da flag de salvamento

    console.log('🎮 Starting game with settings:', {
      gameSettings: { totalRounds, maxGameTime, distance, nextActivationDelay, allowSameDevice },
      deviceSettings: { distanceThreshold, globalTimeout, lastResortTimeout }
    });

    const currentNextActivationDelay = nextActivationDelayRef.current;
    toast({
      title: "🎮 Jogo Iniciado!",
      description: currentNextActivationDelay > 0 ? `Aguarde ativação... (${currentNextActivationDelay}ms)` : "Primeiro sensor ativando..."
    });

    // Definir estado como running - o useEffect vai ativar o primeiro dispositivo
    setGameState('running');

  }, [mqttStatus, participantName, devices, toast, activateNextDevice]);

  // Parar jogo
  const stopGame = useCallback(() => {
    setGameState('idle');
    setActiveDevice(null);
    toast({ title: "🛑 Jogo Parado", description: "Jogo interrompido" });
  }, [toast]);

  // Reset jogo
  const resetGame = useCallback(() => {
    setGameState('idle');
    setCurrentRound(0);
    setSuccesses(0);
    setMisses(0);
    setReactions([]);
    setActiveDevice(null);
    setGameTime(0);
    setWaitingForReaction(false);
    gameSavedRef.current = false; // Reset da flag de salvamento

    // Limpar timers
    if (timeoutTimerRef.current) {
      clearTimeout(timeoutTimerRef.current);
      setTimeoutTimer(null);
    }
    if (nextActivationTimeoutRef.current) {
      clearTimeout(nextActivationTimeoutRef.current);
      nextActivationTimeoutRef.current = null;
    }

    // Reset do controle de ativação
    activationInProgressRef.current = false;
  }, []);

  // Configurar dispositivos
  const configureDevices = useCallback(() => {
    const activeDevices = devices.filter(d => d.active);
    if (activeDevices.length === 0) {
      toast({ title: "Erro", description: "Nenhum dispositivo ativo!", variant: "destructive" });
      return;
    }

    console.log('📤 Sending device configuration:', {
      distanceThreshold_cm: distanceThreshold,
      globalTimeout_ms: globalTimeout,
      lastResortTimeout_ms: lastResortTimeout,
      firmwareBehavior: {
        ledOnPeriod: `0-${globalTimeout}ms`,
        ledBlinkingPeriod: `${globalTimeout}-${globalTimeout + lastResortTimeout}ms`,
        totalTime: `${globalTimeout + lastResortTimeout}ms`
      }
    });

    activeDevices.forEach(device => {
      const config = {
        command: "configure",
        parameters: {
          distancethreshold: distanceThreshold, // valor em cm (não converter)
          globaltimeout: globalTimeout, // LED aceso
          lastresorttimeout: lastResortTimeout // LED piscando (adicional)
        }
      };

      console.log(`📡 Sending config to ${device.mac}:`, config);
      sendCommandToDevice(device.mac, config);
    });

    toast({
      title: "✅ Configuração de Dispositivos Enviada!",
      description: `${activeDevices.length} sensores: alcance ${distanceThreshold}cm, tempo total ${globalTimeout + lastResortTimeout}ms`
    });
  }, [devices, distanceThreshold, globalTimeout, lastResortTimeout, sendCommandToDevice, toast]);

  // Debug log removido para evitar loop infinito

  return {
    // Estado
    gameState,
    currentRound,
    totalRounds,
    gameTime,
    successes,
    misses,
    reactions,
    activeDevice,
    waitingForReaction,
    timeoutTimer,

    // Versões para forçar re-render
    devicesVersion,
    configVersion,

    // ===== CONFIGURAÇÕES DE JOGO =====
    totalRounds,
    setTotalRounds,
    maxGameTime,
    setMaxGameTime,
    distance,
    setDistance,
    nextActivationDelay,
    setNextActivationDelay,
    allowSameDevice,
    setAllowSameDevice,

    // ===== CONFIGURAÇÕES DE DISPOSITIVOS =====
    distanceThreshold,
    setDistanceThreshold,
    globalTimeout,
    setGlobalTimeout,
    lastResortTimeout,
    setLastResortTimeout,

    // Ações
    startGame,
    stopGame,
    resetGame,
    processReaction,
    configureDevices,
    toggleDeviceActive,
    saveGameManually,
    clearCorruptedData,
    debugLocalStorage,

    // Estatísticas
    accuracy: reactions.length > 0 ? (successes / reactions.length) * 100 : 0,
    averageReactionTime: reactions.filter(r => r.success).length > 0
      ? reactions.filter(r => r.success).reduce((sum, r) => sum + r.reactionTime, 0) / reactions.filter(r => r.success).length
      : 0,
    maxSpeed: reactions.length > 0 ? Math.max(...reactions.map(r => r.speed)) : 0,

    // Configurações (legacy)
    settings: {
      // Configurações de Jogo
      totalRounds,
      maxGameTime,
      distance,
      nextActivationDelay,
      allowSameDevice,
      // Configurações de Dispositivos
      distanceThreshold,
      globalTimeout,
      lastResortTimeout
    }
  };
};
