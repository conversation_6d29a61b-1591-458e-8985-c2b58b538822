import { useState, useEffect, useRef, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useMqtt } from './mqtt/useMqtt';
import { useParticipantManager } from './useParticipantManager';
import { COMMAND_TOPIC_PREFIX, TELEMETRY_TOPIC_PREFIX } from './mqtt/constants';
import { validateDistance, validateParticipantName, validateMacAddress, validateSpeedCalculation, validateTimestamp } from '@/lib/validation';
import { safeLocalStorageGet, safeLocalStorageSet } from '@/lib/storage';

const DEFAULT_DEVICES = [
  { mac: '7C:DF:A1:08:D7:74', name: 'Sensor 1', status: 'offline', active: true },
  { mac: 'A0:76:4E:92:4D:42', name: 'Sensor 2', status: 'offline', active: true },
  // Add your new devices here - replace with actual MAC addresses
  // { mac: 'XX:XX:XX:XX:XX:XX', name: 'Sensor 3', status: 'offline', active: true },
  // { mac: 'YY:YY:YY:YY:YY:YY', name: 'Sensor 4', status: 'offline', active: true },
];

export const useSpeedTracker = ({ distance, participantName, onNewMeasurement, onReactionDetected }) => {
  const { toast } = useToast();
  const { participants, addParticipant, updateParticipant, addRunToParticipant, addReactionGameToParticipant } = useParticipantManager();
  
  const [devices, setDevices] = useState(() => {
    const savedDevices = safeLocalStorageGet('speedDevices', DEFAULT_DEVICES);
    // Ensure status is always offline on start and validate device data
    return savedDevices.map(d => ({
      ...d,
      status: 'offline',
      mac: d.mac || '',
      name: d.name || '',
      active: typeof d.active === 'boolean' ? d.active : true
    }));
  });
  
  const [timestamps, setTimestamps] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const [logMessages, setLogMessages] = useState([]);
  
  const simulationTimeoutRef = useRef(null);
  
  // Refs for stable callbacks
  const isRunningRef = useRef(isRunning);
  const activeDeviceCountRef = useRef(0);
  const distanceRef = useRef(distance);
  const participantNameRef = useRef(participantName);
  const onNewMeasurementRef = useRef(onNewMeasurement);
  const processingRunRef = useRef(false);

  useEffect(() => { isRunningRef.current = isRunning; }, [isRunning]);
  useEffect(() => { distanceRef.current = distance; }, [distance]);
  useEffect(() => { participantNameRef.current = participantName; }, [participantName]);
  useEffect(() => { onNewMeasurementRef.current = onNewMeasurement; }, [onNewMeasurement]);
  
  useEffect(() => {
    const devicesToSave = devices.map(({ mac, name, active }) => ({ mac, name, active }));
    safeLocalStorageSet('speedDevices', devicesToSave);
  }, [devices]);

  const addLog = useCallback((topic, message) => {
    setLogMessages(prevLogs => {
      const newLog = { id: Date.now(), topic, message, receivedAt: new Date().toISOString() };
      return [newLog, ...prevLogs.slice(0, 99)];
    });
  }, []);

  const calculateAndSave = useCallback((finalTimestampsWithMac) => {
    const currentDistance = distanceRef.current;
    const currentParticipantName = participantNameRef.current;

    // Validate distance
    const distanceValidation = validateDistance(currentDistance);
    if (!distanceValidation.isValid) {
      toast({ title: "⚠️ Distância inválida", description: distanceValidation.error, variant: "destructive" });
      return null;
    }

    // Validate participant name
    const nameValidation = validateParticipantName(currentParticipantName);
    if (!nameValidation.isValid) {
      toast({ title: "⚠️ Nome inválido", description: nameValidation.error, variant: "destructive" });
      return null;
    }

    const requiredSignals = activeDeviceCountRef.current;
    if (finalTimestampsWithMac.length < requiredSignals || finalTimestampsWithMac.length < 2) {
      toast({ title: "❌ Medição cancelada", description: `Recebidos ${finalTimestampsWithMac.length} de ${requiredSignals} sinais.`, variant: "destructive" });
      return null;
    }

    // Validate speed calculation
    const speedValidation = validateSpeedCalculation(finalTimestampsWithMac, distanceValidation.value);
    if (!speedValidation.isValid) {
      toast({ title: "❌ Erro na Medição", description: speedValidation.error, variant: "destructive" });
      return null;
    }

    const { speed: speedInMps, speedKmh, totalTime: totalTimeInSeconds } = speedValidation;

    const sortedPasses = [...finalTimestampsWithMac].sort((a, b) => a.timestamp - b.timestamp);

    const newRun = {
      id: Date.now(),
      participantName: nameValidation.value,
      distance: distanceValidation.value,
      timestamps: sortedPasses.map(p => p.timestamp),
      totalTime: totalTimeInSeconds,
      speed: speedInMps,
      speedKmh: speedKmh,
      date: new Date().toISOString(),
      passOrder: sortedPasses.map(p => p.mac),
    };

    addRunToParticipant(nameValidation.value, newRun);
    toast({ title: "🎉 Medição concluída!", description: `Velocidade: ${speedKmh.toFixed(2)} km/h para ${nameValidation.value}` });
    return newRun;
  }, [toast, addRunToParticipant]);

  const processSignal = useCallback((mac, timestamp) => {
    if (!isRunningRef.current || processingRunRef.current) return;
    
    setTimestamps(prevTimestamps => {
      if (prevTimestamps.some(t => t.mac === mac)) return prevTimestamps;
      
      const newTimestamps = [...prevTimestamps, { mac, timestamp }];
      const requiredSignals = activeDeviceCountRef.current;

      toast({ 
        title: `✅ Sinal recebido! (${newTimestamps.length}/${requiredSignals})`, 
        description: `Dispositivo: ${mac}`, 
      });

      if (newTimestamps.length >= requiredSignals) {
        processingRunRef.current = true; // Block subsequent signals
        setIsRunning(false);
        const newRunData = calculateAndSave(newTimestamps);
        if (newRunData && onNewMeasurementRef.current) {
          onNewMeasurementRef.current(newRunData);
        }
      }
      
      return newTimestamps;
    });
  }, [calculateAndSave, toast]);

  const handleMqttMessage = useCallback((topic, message) => {
    const messageStr = message.toString();
    addLog(topic, messageStr);

    if (topic.startsWith(TELEMETRY_TOPIC_PREFIX)) {
        const topicParts = topic.split('/');
        const mac = topicParts[topicParts.length - 1];

        setDevices(prevDevices => prevDevices.map(d =>
            d.mac === mac && d.status !== 'online' ? { ...d, status: 'online' } : d
        ));

        try {
            const jsonStartIndex = messageStr.indexOf('{');
            if (jsonStartIndex === -1) return;

            let jsonStr = messageStr.substring(jsonStartIndex);
            jsonStr = jsonStr.replace(/(\s*?{\s*?|"s*?,\s*?)(['"])?([a-zA-Z0-9_]+)(['"])?:/g, '$1"$3":');
            const data = JSON.parse(jsonStr);

            if (data.pass === "true" && data.timestamp) {
                console.log('🎯 Sensor activation detected:', { mac, data });
                console.log('🔍 Context:', {
                    isRunning: isRunningRef.current,
                    hasReactionCallback: !!onReactionDetected,
                    rawMessage: messageStr
                });

                const timestampValue = Number(data.timestamp);
                const timestampValidation = validateTimestamp(timestampValue);
                if (timestampValidation.isValid) {
                    console.log('✅ Valid timestamp:', timestampValidation.value);
                    // Para modo velocidade
                    if (isRunningRef.current) {
                        console.log('🏃 Processing for speed mode');
                        processSignal(mac, timestampValidation.value);
                    }
                    // Para modo reação (sempre processa)
                    if (onReactionDetected) {
                        console.log('🎮 Processing for reaction mode');
                        onReactionDetected(mac, timestampValidation.value);
                    }
                } else {
                    addLog("TIMESTAMP ERROR", `Timestamp inválido de ${mac}: ${timestampValidation.error}`);
                }
            } else if (data.active !== undefined) {
                setDevices(prevDevices => prevDevices.map(d =>
                    d.mac === mac ? { ...d, active: data.active } : d
                ));
            }
        } catch (error) {
            addLog("JSON ERROR", `Erro ao processar JSON de ${mac}: ${error.message}`);
        }
    }
  }, [addLog, processSignal, onReactionDetected]);
  
  const { clientRef, mqttStatus, connectMqtt, disconnectMqtt, subscribeToDevice } = useMqtt({ devices, setDevices, onMessage: handleMqttMessage, addLog });
  
  useEffect(() => {
    connectMqtt();
    return () => {
      if (clientRef.current) clientRef.current.end(true);
      if (simulationTimeoutRef.current) clearTimeout(simulationTimeoutRef.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Run only on mount
  
  const sendCommandToDevice = useCallback((mac, command, parameters = {}) => {
    if (!clientRef.current || clientRef.current.connected !== true) {
      toast({ title: "Não ligado", description: "Ligue-se ao MQTT para enviar comandos.", variant: "destructive" });
      return;
    }
    const topic = `${COMMAND_TOPIC_PREFIX}${mac}`;
    const payload = JSON.stringify({ command, ...parameters });
    clientRef.current.publish(topic, payload);
    addLog(topic, payload);
  }, [toast, addLog, clientRef]);

  const toggleDeviceActive = (mac) => {
    setDevices(prevDevices => {
      const newDevices = prevDevices.map(d => d.mac === mac ? { ...d, active: !d.active } : d);

      // Forçar atualização imediata no localStorage
      const devicesToSave = newDevices.map(({ mac, name, active }) => ({ mac, name, active }));
      safeLocalStorageSet('speedDevices', devicesToSave);

      return newDevices;
    });
  };

  const configureAllDevices = (parameters) => {
    devices.forEach(device => {
      if(device.active) {
          sendCommandToDevice(device.mac, 'configure', parameters);
      }
    });
    toast({ title: "Configuração Enviada", description: "Comandos de configuração enviados para os dispositivos ativos." });
  };
  
  const startMeasurement = (isSimulation = false) => {
    const activeDevices = devices.filter(d => d.active);
    const numActiveDevices = activeDevices.length;
    activeDeviceCountRef.current = numActiveDevices;

    if (mqttStatus !== 'Connected' && !isSimulation) {
      toast({ title: "⚠️ Não ligado", description: "Ligue-se ao MQTT primeiro ou use a simulação!", variant: "destructive" });
      return;
    }

    // Validate inputs before starting
    const distanceValidation = validateDistance(distance);
    if (!distanceValidation.isValid) {
      toast({ title: "⚠️ Distância inválida", description: distanceValidation.error, variant: "destructive" });
      return;
    }

    const nameValidation = validateParticipantName(participantName);
    if (!nameValidation.isValid) {
      toast({ title: "⚠️ Nome inválido", description: nameValidation.error, variant: "destructive" });
      return;
    }
    if (numActiveDevices < 2) {
      toast({ title: "⚠️ Dispositivos insuficientes", description: `São necessários pelo menos 2 dispositivos ativos. Ativos: ${numActiveDevices}.`, variant: "destructive" });
      return;
    }
    
    if (onNewMeasurementRef.current) onNewMeasurementRef.current(null);
    processingRunRef.current = false;
    setIsRunning(true);
    setTimestamps([]);
    
    if (isSimulation) {
      toast({ title: "🚀 Simulação iniciada!", description: `Aguardando ${numActiveDevices} sinais simulados para ${participantName}.` });
      
      const simulateStep = (index) => {
        if (!isRunningRef.current || index >= activeDevices.length) {
          if (simulationTimeoutRef.current) clearTimeout(simulationTimeoutRef.current);
          return;
        }
        processSignal(activeDevices[index].mac, Date.now() + index * 100);
        simulationTimeoutRef.current = setTimeout(() => simulateStep(index + 1), 700 + Math.random() * 800);
      };
      simulationTimeoutRef.current = setTimeout(() => simulateStep(0), 500);
    } else {
        activeDevices.forEach(device => {
          sendCommandToDevice(device.mac, 'activate');
        });
        toast({ title: "🚀 Medição iniciada!", description: `Aguardando ${numActiveDevices} sinais para ${participantName}.` });
    }
  };

  const resetMeasurement = () => {
    setIsRunning(false);
    setTimestamps([]);
    if (onNewMeasurementRef.current) onNewMeasurementRef.current(null);
    if (simulationTimeoutRef.current) {
      clearTimeout(simulationTimeoutRef.current);
      simulationTimeoutRef.current = null;
    }
    devices.forEach(device => {
      if(device.active) {
          sendCommandToDevice(device.mac, 'reset');
      }
    });
    toast({ title: "🔄 Medição resetada", description: "Pronto para uma nova medição!" });
  };

  const addDevice = ({ mac, name }) => {
    // Validate MAC address
    const macValidation = validateMacAddress(mac);
    if (!macValidation.isValid) {
      toast({ title: "Erro", description: macValidation.error, variant: "destructive" });
      return;
    }

    // Check for duplicates
    if (devices.some(d => d.mac === macValidation.value)) {
      toast({ title: "Erro", description: "Dispositivo já existe.", variant: "destructive" });
      return;
    }

    // Validate and sanitize name
    const sanitizedName = name ? name.trim().substring(0, 30) : '';

    const newDevice = {
      mac: macValidation.value,
      name: sanitizedName,
      status: 'offline',
      active: true
    };
    setDevices(prevDevices => [...prevDevices, newDevice]);
    subscribeToDevice(macValidation.value);
  };

  const updateDevice = (mac, newName) => {
    // Sanitize the new name
    const sanitizedName = newName ? newName.trim().substring(0, 30) : '';

    setDevices(prevDevices =>
      prevDevices.map(d => (d.mac === mac ? { ...d, name: sanitizedName } : d))
    );
  };

  const clearLogs = () => setLogMessages([]);

  // Função para publicar mensagens MQTT
  const publishMessage = useCallback((topic, payload) => {
    if (!clientRef.current || !clientRef.current.connected) {
      toast({ title: "Não ligado", description: "Ligue-se ao MQTT para enviar comandos.", variant: "destructive" });
      return;
    }

    // Se payload é string, enviar diretamente; se é objeto, converter para JSON
    const messagePayload = typeof payload === 'string' ? payload : JSON.stringify(payload);

    clientRef.current.publish(topic, messagePayload);
    addLog("COMMAND", `${topic}${messagePayload}`);
  }, [toast, addLog, clientRef]);

  return {
    timestamps,
    participants,
    addParticipant,
    updateParticipant,
    addReactionGameToParticipant,
    isRunning,
    startMeasurement,
    resetMeasurement,
    mqttStatus,
    connectMqtt,
    disconnectMqtt,
    devices,
    addDevice,
    updateDevice,
    toggleDeviceActive,
    configureAllDevices,
    logMessages,
    clearLogs,
    publishMessage,
    addLog,
  };
};