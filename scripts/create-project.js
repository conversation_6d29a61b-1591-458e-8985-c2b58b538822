#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function createProject(projectName) {
  const projectDir = path.join(process.cwd(), projectName);
  
  // Create directory structure
  const dirs = [
    'src/components',
    'src/hooks',
    'src/config',
    'src/lib',
    'public'
  ];
  
  dirs.forEach(dir => {
    fs.mkdirSync(path.join(projectDir, dir), { recursive: true });
  });
  
  console.log(`✅ Project ${projectName} created!`);
}

const projectName = process.argv[2];
if (!projectName) {
  console.error('Please provide a project name');
  process.exit(1);
}

createProject(projectName);