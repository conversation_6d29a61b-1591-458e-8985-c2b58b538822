f# YTK Speed Tracker - Environment Configuration
# Copy this file to .env and fill in your actual credentials

# MQTT Configuration
VITE_MQTT_BROKER_URL=wss://your-mqtt-broker.com:8884/mqtt
VITE_MQTT_USERNAME=your_username
VITE_MQTT_PASSWORD=your_password

# Application Settings
VITE_APP_NAME=YTK Speed Tracker
VITE_APP_VERSION=1.0.0
VITE_DEBUG_MODE=false

# Analytics (optional)
VITE_ANALYTICS_ID=your_analytics_id

# Feature Flags
VITE_ENABLE_SIMULATION=true
VITE_ENABLE_DEBUG_LOGS=false
VITE_MAX_DEVICES=10
